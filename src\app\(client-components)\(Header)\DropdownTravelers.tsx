"use client"

import type React from "react"

import { Popover, Transition } from "@headlessui/react"
import { ChevronDownIcon } from "@heroicons/react/24/solid"
import { Fragment, useState, useEffect } from "react"
import type { PathName } from "@/routers/types"
import Link from "next/link"
import Image from "next/image"
import { usePathname } from "next/navigation"
import { useSearch } from "@/app/(stay-listings)/SearchContext"
import { useRouter } from "next/navigation"

interface MegamenuItem {
  id: string
  image: string
  title: string
  items: NavItemType[]
}

interface NavItemType {
  id: string
  name: string
  isNew?: boolean
  href: PathName
  targetBlank?: boolean
  children?: NavItemType[]
  megaMenu?: MegamenuItem[]
  type?: "dropdown" | "megaMenu" | "none"
}

interface PropertyType {
  id: string
  name: string
}

export default function DropdownTravelers() {
  const [isHovering, setIsHovering] = useState(false)
  const [propertyTypes, setPropertyTypes] = useState<PropertyType[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [megaMenuItems, setMegaMenuItems] = useState<MegamenuItem[]>([])
  const router = useRouter()
  const { searchParams, setSearchParams } = useSearch()

  // CLOSE MENU WHEN ROUTE CHANGES
  const locationPathName = usePathname()
  useEffect(() => {
    setIsHovering(false)
  }, [locationPathName])

  // Lazy load the content only when the dropdown is hovered
  useEffect(() => {
    if (isHovering && isLoading) {
      loadMenuContent()
    }
  }, [isHovering, isLoading])

  const loadMenuContent = async () => {
    try {
      setIsLoading(true)

      // Fetch property types
      const response = await fetch("/api/listing/types", { method: "GET" })
      const data = await response.json()

      if (!response.ok) {
        console.error(data.error || "Unknown error")
        return
      }

      const fetchedPropertyTypes = data.types || []
      setPropertyTypes(fetchedPropertyTypes)

      // Create megamenu items with the fetched property types
      const menuItems: MegamenuItem[] = [
        {
          id: "destinations",
          image: "https://api.almindharbooking.com/storage/v1/object/public/listing-categories//camping.png",
          title: "Destinations",
          items: [
            { id: "Tunis", name: "Tunis", href: "/listing-stay-map" },
            { id: "Djerba", name: "Djerba", href: "/listing-stay-map" },
            { id: "Sousse", name: "Sousse", href: "/listing-stay-map" },
            { id: "Hammamet", name: "Hammamet", href: "/listing-stay-map" },
            { id: "Monastir", name: "Monastir", href: "/listing-stay-map" },
          ],
        },
        {
          id: "type",
          image: "https://api.almindharbooking.com/storage/v1/object/public/uploads//pool.jpg",
          title: "Type de logement",
          items: fetchedPropertyTypes.map((property: PropertyType) => ({
            id: property.id,
            name: property.name,
            href: "/listing-stay-map",
          })),
        },
        {
          id: "type-sejour",
          image: "https://api.almindharbooking.com/storage/v1/object/public/uploads//farm.jpg",
          title: "Type de séjour",
          items: [
            { id: "vacances", name: "Vacances", href: "/listing-stay-map" },
            { id: "deplacement-professionnel", name: "Déplacement professionnel", href: "/listing-stay-map" },
            { id: "sejour-medical", name: "Séjour médical", href: "/listing-stay-map" },
            { id: "evénement-special", name: "Événement spécial", href: "/listing-stay-map" },
          ],
        },
      ]

      setMegaMenuItems(menuItems)
      setIsLoading(false)
    } catch (err) {
      console.error("Network error", err)
      setIsLoading(false)
    }
  }

  const handlePropertyTypeClick = (propertyTypeId: string) => {
    setSearchParams((prev) => ({ ...prev, propertyTypes: [propertyTypeId] }))
    router.push("/listing-stay-map")
  }

  const handleDestinationClick = (destinationId: string) => {
    setSearchParams((prev) => ({ ...prev, location: destinationId }))
    router.push("/listing-stay-map")
  }

  const renderMegaMenuNavlink = (item: NavItemType, sectionId?: string) => {
    // Check if this is a property type item or a destination item
    const isPropertyType = sectionId === "type"
    const isDestination = sectionId === "destinations"

    const handleClick = (e: React.MouseEvent) => {
      if (isPropertyType) {
        e.preventDefault()
        handlePropertyTypeClick(item.id)
      } else if (isDestination) {
        e.preventDefault()
        handleDestinationClick(item.id)
      }
    }

    return (
        <li key={item.id}>
          <Link
              rel="noopener noreferrer"
              className="inline-flex items-center py-1 px-2 rounded hover:text-neutral-700 hover:bg-neutral-100 dark:hover:bg-neutral-800 dark:hover:text-neutral-200 font-normal text-neutral-6000 dark:text-neutral-300"
              href={item.href || ""}
              onClick={handleClick}
          >
            {item.name}
          </Link>
        </li>
    )
  }

  const renderLoadingState = () => (
      <div className="relative bg-white dark:bg-neutral-900 px-3 py-6 grid gap-1 grid-cols-3">
        {[1, 2, 3].map((i) => (
            <div key={i} className="animate-pulse">
              <div className="px-2">
                <div className="w-36 h-24 rounded-lg bg-neutral-200 dark:bg-neutral-800"></div>
              </div>
              <div className="h-5 bg-neutral-200 dark:bg-neutral-800 rounded w-24 my-3 mx-2"></div>
              <ul className="grid space-y-1">
                {[1, 2, 3, 4].map((j) => (
                    <li key={j} className="h-4 bg-neutral-100 dark:bg-neutral-700 rounded w-32 mx-2 my-1"></li>
                ))}
              </ul>
            </div>
        ))}
      </div>
  )

  return (
      <Popover
          className="DropdownTravelers relative flex"
          onMouseEnter={() => setIsHovering(true)}
          onMouseLeave={() => setIsHovering(false)}
      >
        {({ close }) => (
            <>
              <Popover.Button
                  className={`${isHovering ? "text-neutral-900 bg-neutral-100" : "text-opacity-90"}
              rounded-full py-2 px-4 xl:px-5 group self-center h-10 sm:h-12 text-sm sm:text-base font-medium hover:text-opacity-100 focus:outline-none`}
              >
                <div className={` inline-flex items-center  `} role="button">
                  <span>Voyageurs</span>
                  <ChevronDownIcon
                      className={`${isHovering ? "-rotate-180" : "text-opacity-70 "}
                ml-2 h-5 w-5 text-neutral-700 group-hover:text-opacity-80 transition ease-in-out duration-150 `}
                      aria-hidden="true"
                  />
                </div>
              </Popover.Button>
              <Transition
                  as={Fragment}
                  show={isHovering}
                  enter="transition ease-out duration-150"
                  enterFrom="opacity-0 translate-y-1"
                  enterTo="opacity-100 translate-y-0"
                  leave="transition ease-in duration-150"
                  leaveFrom="opacity-100 translate-y-0"
                  leaveTo="opacity-0 translate-y-1"
              >
                <Popover.Panel className="will-change-transform sub-menu absolute top-full transform z-10 w-screen max-w-screen-lg px-4 sm:px-0 lg:max-w-max -translate-x-1/2 left-1/2">
                  <div className="overflow-hidden rounded-lg shadow-lg ring-1 ring-black dark:ring-white ring-opacity-5 dark:ring-opacity-10 text-sm">
                    {isLoading ? (
                        renderLoadingState()
                    ) : (
                        <div className="relative bg-white dark:bg-neutral-900 px-3 py-6 grid gap-1 grid-cols-3">
                          {megaMenuItems.map((item) => (
                              <div key={item.id}>
                                <div className="px-2">
                                  <div className="w-36 h-24 rounded-lg overflow-hidden relative flex">
                                    <Image
                                        alt={item.title}
                                        src={item.image || "/placeholder.svg"}
                                        fill
                                        sizes="200px"
                                        loading="lazy"
                                    />
                                  </div>
                                </div>
                                <p className="font-medium text-neutral-900 dark:text-neutral-200 py-1 px-2 my-2">
                                  {item.title}
                                </p>
                                <ul className="grid space-y-1">
                                  {item.items.map((navItem) => renderMegaMenuNavlink(navItem, item.id))}
                                </ul>
                              </div>
                          ))}
                        </div>
                    )}
                  </div>
                </Popover.Panel>
              </Transition>
            </>
        )}
      </Popover>
  )
}

