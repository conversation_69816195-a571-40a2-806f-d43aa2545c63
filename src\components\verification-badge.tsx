"use client"

import { ShieldCheck } from "lucide-react"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { cn } from "@/lib/utils"

interface VerificationBadgeProps {
  verified: boolean
  size?: "sm" | "md" | "lg"
  showTooltip?: boolean
  className?: string
}

export function VerificationBadge({
  verified,
  size = "md",
  showTooltip = true,
  className,
}: VerificationBadgeProps) {
  const sizeClasses = {
    sm: "w-4 h-4",
    md: "w-5 h-5",
    lg: "w-6 h-6",
  }

  const badge = (
    <div
      className={cn(
        "inline-flex items-center justify-center rounded-full",
        verified ? "bg-green-100 text-green-600" : "bg-gray-100 text-gray-400",
        className
      )}
    >
      <ShieldCheck className={sizeClasses[size]} />
    </div>
  )

  if (!showTooltip) {
    return badge
  }

  return (
    <TooltipProvider>
      <Tooltip delayDuration={300}>
        <TooltipTrigger asChild>
          {badge}
        </TooltipTrigger>
        <TooltipContent side="top" align="center" className="text-xs font-medium">
          {verified 
            ? "Identité vérifiée" 
            : "Identité non vérifiée"}
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  )
}
