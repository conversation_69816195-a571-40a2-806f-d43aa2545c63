'use client';

import React, { FC, useState, useCallback, useMemo } from "react";
import Image, { StaticImageData } from "next/image";
import { avatarColors } from "@/contains/contants";

export interface AvatarProps {
    containerClassName?: string;
    sizeClass?: string;
    radius?: string;
    imgUrl?: string | StaticImageData | null;
    userName?: string | null;
    hasChecked?: boolean;
    hasCheckedClass?: string;
    priority?: boolean; 
    verificationStatus?: "not_submitted" | "pending" | "verified" | "rejected";
}

const Avatar: FC<AvatarProps> = ({
    containerClassName = "ring-1 ring-white dark:ring-neutral-900",
    sizeClass = "h-6 w-6",
    radius = "rounded-full",
    imgUrl,
    userName,
    hasChecked,
    hasCheckedClass = "w-4 h-4 absolute right-0 -bottom-1 ring-2 ring-white rounded-full",
    priority = false,
    verificationStatus,
}) => {
    const [imageLoaded, setImageLoaded] = useState(false);
    const [imageError, setImageError] = useState(false);
    const [retryCount, setRetryCount] = useState(0);
    const name = userName || "Anonymous";

    // Create a cached version of the image URL with a cache buster if it's from Supabase
    const cachedImgUrl = useMemo(() => {
        if (!imgUrl || typeof imgUrl !== 'string') return imgUrl;
        
        // Only add cache parameter for Supabase URLs to prevent rate limiting
        if (imgUrl.includes('supabase.co') || imgUrl.includes('almindharbooking.com')) {
            // Extract existing URL and add or update cache parameter
            try {
                const url = new URL(imgUrl);
                // Add a long cache time parameter (30 days)
                url.searchParams.set('_cache', 'max-age=2592000');
                return url.toString();
            } catch (e) {
                // If URL parsing fails, return original
                return imgUrl;
            }
        }
        
        return imgUrl;
    }, [imgUrl]);
    
    const getBgColor = () => {
        const index = Math.floor(name.charCodeAt(0) % avatarColors.length);
        return avatarColors[index];
    };

    // Reset states when imgUrl changes
    React.useEffect(() => {
        setImageLoaded(false);
        setImageError(false);
        setRetryCount(0);
    }, [imgUrl]);

    // Handle image load success
    const handleImageLoad = () => {
        setImageLoaded(true);
    };

    // Handle image load failure with exponential backoff retry
    const handleImageError = useCallback(() => {
        // Implement exponential backoff for retries
        if (retryCount < 3) {
            const retryDelay = Math.pow(2, retryCount) * 1000; // Exponential backoff
            
            setTimeout(() => {
                setRetryCount(prev => prev + 1);
                // Force re-render of image by updating state
                setImageError(false);
            }, retryDelay);
        } else {
            // After max retries, show fallback
            setImageError(true);
        }
    }, [retryCount]);

    // Determine badge color based on verification status
    const getBadgeColor = () => {
        if (!verificationStatus) {
            return hasChecked ? "bg-teal-500" : "";
        }

        switch(verificationStatus) {
            case "verified":
                return "bg-teal-500";
            case "pending":
                return "bg-amber-500";
            case "rejected":
                return "bg-red-500";
            default:
                return "bg-gray-400";
        }
    };

    // Determine if badge should be shown
    const showBadge = hasChecked || (verificationStatus && verificationStatus !== "not_submitted");

    return (
        <div
            className={`relative flex-shrink-0 inline-flex items-center justify-center text-neutral-100 uppercase font-semibold shadow-inner ${radius} ${containerClassName} ${sizeClass}`}
            style={{ backgroundColor: getBgColor() }}
        >
            {/* Show initial while image is loading or if there's no image */}
            {(!cachedImgUrl || !imageLoaded || imageError) && (
                <span className="text-sm">{name[0].toUpperCase()}</span>
            )}
            
            {/* Image with loading handling */}
            {cachedImgUrl && !imageError && (
                <Image
                    className={`absolute inset-0 w-full h-full object-cover transition-opacity duration-200 rounded-full ${
                        imageLoaded ? 'opacity-100' : 'opacity-0'
                    }`}
                    src={cachedImgUrl}
                    alt={userName || "Avatar"}
                    width={100}
                    height={100}
                    onLoad={handleImageLoad}
                    onError={handleImageError}
                    priority={priority}
                    loading={priority ? "eager" : "lazy"}
                    // Add proper caching directives
                    unoptimized={typeof cachedImgUrl === 'string' && 
                                (cachedImgUrl.includes('supabase.co') || cachedImgUrl.includes('almindharbooking.com'))}
                />
            )}

            {/* Verification badge */}
            {showBadge && (
                <span
                    className={`absolute ${getBadgeColor()} rounded-full text-white text-xs flex items-center justify-center ${hasCheckedClass}`}
                >
                    <svg 
                        className="w-3 h-3" 
                        fill="none" 
                        stroke="currentColor" 
                        viewBox="0 0 24 24"
                    >
                        <path 
                            strokeLinecap="round" 
                            strokeLinejoin="round" 
                            strokeWidth={2} 
                            d="M5 13l4 4L19 7" 
                        />
                    </svg>
                </span>
            )}
        </div>
    );
};

export default Avatar;