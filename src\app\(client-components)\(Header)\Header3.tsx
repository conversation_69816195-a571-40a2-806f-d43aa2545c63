"use client"

import { type FC, useEffect, useRef, useState, memo } from "react"
import Logo from "@/shared/Logo"
import useOutsideAlerter from "@/hooks/useOutsideAlerter"
import NotifyDropdown from "./NotifyDropdown"
import AvatarDropdown from "./AvatarDropdown"
import MenuBar from "@/shared/MenuBar"
import type { SearchTab } from "../(HeroSearchForm)/HeroSearchForm"
import Link from "next/link"
import { usePathname, useRouter } from "next/navigation"
import HeroSearchFormSmall from "../(HeroSearchFormSmall)/HeroSearchFormSmall"
import type { StaySearchFormFields } from "../type"
import { MagnifyingGlassIcon } from "@heroicons/react/24/outline"
import { useUser } from "@/contexts/UserContext"
import DropdownTravelers from "@/app/(client-components)/(Header)/DropdownTravelers"
import { useSearch } from "@/app/(stay-listings)/SearchContext"
import type { Profile } from "@/types/interfaces"

interface Header3Props {
  className?: string
}

let WIN_PREV_POSITION = 0
if (typeof window !== "undefined") {
  WIN_PREV_POSITION = (window as any).pageYOffset
}

const Header3: FC<Header3Props> = memo(({ className = "" }) => {
  const { userProfile, isLoading } = useUser()
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const { searchParams } = useSearch()
  const pathname = usePathname()
  const router = useRouter()

  // Update isAuthenticated when userProfile changes
  useEffect(() => {
    setIsAuthenticated(!!userProfile)
  }, [userProfile])

  const headerInnerRef = useRef<HTMLDivElement>(null)
  const [showHeroSearch, setShowHeroSearch] = useState<StaySearchFormFields | null>()
  const [currentTab, setCurrentTab] = useState<SearchTab>("Stays")
  const [isSearchVisible, setIsSearchVisible] = useState(false)

  useOutsideAlerter(headerInnerRef, () => {
    setShowHeroSearch(null)
    setCurrentTab("Stays")
  })

  useEffect(() => {
    setShowHeroSearch(null)

    // If not on home page, always show search
    if (pathname !== "/") {
      setIsSearchVisible(true)
    } else {
      // On home page, check initial scroll position
      if (typeof window !== "undefined") {
        const viewportHeight = window.innerHeight
        setIsSearchVisible(window.scrollY > viewportHeight)
      }
    }
  }, [pathname])

  useEffect(() => {
    const handleScroll = () => {
      window.requestAnimationFrame(() => {
        handleHideSearchForm()

        // Only update search visibility based on scroll for home page
        if (pathname === "/") {
          const viewportHeight = window.innerHeight
          setIsSearchVisible(window.scrollY > viewportHeight)
        }
      })
    }

    window.addEventListener("scroll", handleScroll)
    return () => {
      window.removeEventListener("scroll", handleScroll)
    }
  }, [pathname])

  const handleHideSearchForm = () => {
    if (!document.querySelector("#nc-Header-3-anchor")) {
      return
    }
    const currentScrollPos = window.pageYOffset
    if (WIN_PREV_POSITION - currentScrollPos > 100 || WIN_PREV_POSITION - currentScrollPos < -100) {
      setShowHeroSearch(null)
    }
    WIN_PREV_POSITION = currentScrollPos
  }

  const renderHeroSearch = () => {
    return (
        <div
            className={`absolute inset-x-0 top-0 transition-all will-change-[transform,opacity] ${
                showHeroSearch
                    ? "visible"
                    : "-translate-x-0 -translate-y-[90px] scale-x-[0.395] scale-y-[0.6] opacity-0 invisible pointer-events-none"
            }`}
        >
          <div className="w-full max-w-4xl mx-auto pb-6">
            <HeroSearchFormSmall
                defaultFieldFocus={showHeroSearch || undefined}
                onTabChange={setCurrentTab}
                defaultTab={currentTab}
            />
          </div>
        </div>
    )
  }

  const renderButtonOpenHeroSearch = () => {
    // Format the location
    const locationText = searchParams.location || "Localisation"

    // Format the dates
    let dateText = "Arrivée"
    if (searchParams.checkIn) {
      dateText = searchParams.checkIn.toLocaleDateString("fr-FR", {
        month: "short",
        day: "2-digit",
      })

      if (searchParams.checkOut) {
        dateText +=
            " - " +
            searchParams.checkOut.toLocaleDateString("fr-FR", {
              month: "short",
              day: "2-digit",
            })
      }
    }

    // Format the guests
    let guestsText = "Ajouter des invités"
    const totalGuests =
        (searchParams.guests?.guestAdults || 0) +
        (searchParams.guests?.guestChildren || 0) +
        (searchParams.guests?.guestInfants || 0)

    if (totalGuests > 0) {
      guestsText = `${totalGuests} Invité${totalGuests > 1 ? "s" : ""}`
    }

    return (
        <div
            className={`w-full relative flex items-center justify-between border border-neutral-200 dark:border-neutral-6000 rounded-full shadow hover:shadow-md transition-all backdrop-blur-sm bg-white/80 dark:bg-neutral-800/80 ${
                showHeroSearch
                    ? "-translate-x-0 translate-y-20 scale-x-[2.55] scale-y-[1.8] opacity-0 pointer-events-none invisible"
                    : "visible"
            }`}
        >
          <div className="flex items-center font-medium text-sm">
          <span onClick={() => setShowHeroSearch("location")} className="block pl-5 pr-4 cursor-pointer py-3">
            {locationText}
          </span>
            <span className="h-5 w-[1px] bg-neutral-300 dark:bg-neutral-700"></span>
            <span onClick={() => setShowHeroSearch("dates")} className="block px-4 cursor-pointer py-3">
            {dateText}
          </span>
            <span className="h-5 w-[1px] bg-neutral-300 dark:bg-neutral-700"></span>
            <span onClick={() => setShowHeroSearch("guests")} className="block px-4 cursor-pointer py-3">
            {guestsText}
          </span>
          </div>

          <div className="flex-shrink-0 ml-auto pr-2 cursor-pointer" onClick={() => setShowHeroSearch("location")}>
          <span className="w-8 h-8 flex items-center justify-center rounded-full bg-booking-orange text-white shadow-sm backdrop-filter backdrop-blur-sm hover:bg-booking-orange/90 transition-colors">
            <MagnifyingGlassIcon className="w-5 h-5" />
          </span>
          </div>
        </div>
    )
  }

  return (
      <>
        <div
            className={`nc-Header nc-Header-3 fixed z-40 top-0 inset-0 bg-black/30 backdrop-blur-sm dark:bg-black/50 transition-opacity will-change-[opacity] ${
                showHeroSearch ? "visible" : "invisible opacity-0 pointer-events-none"
            }`}
        ></div>
        {showHeroSearch && <div id="nc-Header-3-anchor"></div>}
        <header ref={headerInnerRef} className={`sticky top-0 z-50 ${className}`}>
          <div
              className={`bg-white/70 dark:bg-neutral-900/70 backdrop-blur-xl backdrop-filter shadow-sm border-b border-neutral-200/80 dark:border-neutral-700/80 absolute h-full inset-x-0 top-0 transition-transform will-change-[transform,opacity]
          ${showHeroSearch ? "duration-75" : ""}
          ${
                  showHeroSearch
                      ? currentTab === "Cars" || currentTab === "Flights"
                          ? "scale-y-[4.4]"
                          : "scale-y-[3.4]"
                      : ""
              }`}
          >
            {/* Glassmorphism effect elements */}
            <div className="absolute inset-0 overflow-hidden opacity-30 pointer-events-none">
              <span className="absolute left-1/4 top-1/2 bg-booking-orange w-12 h-12 rounded-full mix-blend-multiply filter blur-xl"></span>
              <span className="absolute right-1/4 top-1/3 bg-dark-blue w-12 h-12 rounded-full mix-blend-multiply filter blur-xl"></span>
            </div>
          </div>
          <div className="relative px-0 md:px-4 lg:container sm:h-[88px] flex">
            <div className="flex-1 flex justify-between">
              {/* Logo (lg+) */}
              <div className="relative z-10 hidden md:flex flex-1 items-center">
                {!isSearchVisible ? (
                    <div className="hidden md:flex justify-start flex-1 items-center space-x-3 sm:space-x-8 lg:space-x-10">
                      <Logo />
                      <div className="hidden lg:block h-10 border-l border-neutral-300 dark:border-neutral-500"></div>
                      <div className="hidden lg:block">
                        <DropdownTravelers />
                      </div>
                    </div>
                ) : (
                    <Logo />
                )}
              </div>

              <div className="flex flex-[2] lg:flex-none mx-auto">
                {!isSearchVisible ? (
                    <></>
                ) : (
                    <div className="flex-1 hidden lg:flex self-center">{renderButtonOpenHeroSearch()}</div>
                )}
                {renderHeroSearch()}
              </div>

              {/* NAV */}
              <div className="hidden md:flex relative z-10 flex-1 justify-end text-neutral-700 dark:text-neutral-100">
                <div className="flex items-center space-x-1">
                  {isAuthenticated && !showHeroSearch && (
                      <Link
                          href="/add-listing/intro"
                          className="self-center hidden xl:inline-flex px-4 py-2 border border-neutral-300 hover:border-neutral-400 dark:border-neutral-700 rounded-full items-center text-sm text-gray-700 dark:text-neutral-300 font-medium focus:outline-none focus-visible:ring-2 focus-visible:ring-white focus-visible:ring-opacity-75"
                      >
                        Ajoutez votre propriété
                      </Link>
                  )}

                  {isAuthenticated && <NotifyDropdown className="mx-1.5" />}

                  {/* Avatar that links directly to account page */}
                  <AvatarDropdown
                      key={`avatar-${userProfile?.id || "guest"}`}
                      userProfile={userProfile as Profile | undefined}
                      isLoading={isLoading}
                  />

                  <MenuBar />
                </div>
              </div>
            </div>
          </div>
        </header>
      </>
  )
})

Header3.displayName = "Header3"

export default Header3
