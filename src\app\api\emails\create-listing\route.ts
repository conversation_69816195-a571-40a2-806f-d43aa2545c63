import { NextResponse } from 'next/server';
import { SupportedLanguage } from '@/utils/email';
import { emailService } from '@/utils/emailService';
import { generateCreateListingEmailHtml } from '@/components/emails/CreateListingEmail';

interface CreateListingEmailRequest {
    email: string;
    name?: string;
    language?: SupportedLanguage;
}

/**
 * API route handler for sending the "Create Listing" email
 * This email encourages new users to create their first property listing
 */
export async function POST(req: Request) {
    try {
        // Extract request data
        const { email, name, language = 'fr' } = await req.json() as CreateListingEmailRequest;

        // Validate required fields
        if (!email) {
            console.error('Create listing email request missing email');
            return NextResponse.json(
                { error: 'Email is required' },
                { status: 400 }
            );
        }

        // Generate email HTML content using the component's helper function
        const emailHtml = generateCreateListingEmailHtml({ name, language });

        // Send the email
        const result = await emailService.sendEmail({
            to: email,
            subject: language === 'en' ? 'Start hosting on Almindhar Booking' : 
                    language === 'ar' ? 'ابدأ بالاستضافة على المندهر بوكينج' : 
                    'Commencez à héberger sur Almindhar Booking',
            html: emailHtml,
        });

        // Handle sending errors
        if (!result.success) {
            console.error("Error sending create listing email:", {
                error: result.error,
                email,
                name,
                language
            });
            
            return NextResponse.json(
                { error: 'Failed to send email' },
                { status: 500 }
            );
        }

        // Return success response
        return NextResponse.json(
            { success: true },
            { status: 200 }
        );
    } catch (error) {
        console.error("Exception in create listing email route:", error);
        
        return NextResponse.json(
            { error: 'Internal server error' },
            { status: 500 }
        );
    }
}