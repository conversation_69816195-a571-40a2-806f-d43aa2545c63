import { type NextRequest, NextResponse } from "next/server"
import { supabaseAdmin } from "@/lib/supabase"
import { emailService } from "@/utils/emailService"
import { generateIdentityVerificationEmail } from "@/components/emails/IdentityVerificationEmail"
import { shouldSendEmail, logSkippedEmail } from "@/utils/emailPreferences"

export async function POST(request: NextRequest) {
  try {
    const { id, status, rejectionReason } = await request.json()
    console.log("Received request to update verification:", { id, status, rejectionReason })

    if (!id || !status) {
      return NextResponse.json({ error: "Missing required fields" }, { status: 400 })
    }

    // Special handling for rejection
    if (status === "rejected") {
      console.log("Handling rejection with direct database update")

      // Directly set the status to "declined" for rejections
      const updateData = {
        status: "declined", // Use "declined" as shown in the database
        updated_at: new Date().toISOString(),
        rejected_at: new Date().toISOString(),
        verified_at: null,
        // Store the rejection reason
        rejection_reason: rejectionReason || "Rejected by admin",
      }

      console.log("Rejection update data:", updateData)

      // Use supabaseAdmin to bypass RLS policies
      const { data, error } = await supabaseAdmin
        .from("identity_verifications")
        .update(updateData)
        .eq("id", id)
        .select()

      if (error) {
        console.error("Error rejecting verification:", error)
        return NextResponse.json({ error: error.message }, { status: 500 })
      }

      console.log("Rejection successful, returned data:", data)
      
      // Send email notification for rejection
      console.log("Attempting to send rejection email notification")
      try {
        await sendVerificationEmail(data[0], "rejected", rejectionReason)
        console.log("Email notification sent successfully")
      } catch (emailError) {
        console.error("Failed to send email notification:", emailError)
      }
      
      return NextResponse.json({ success: true, verification: data[0] })
    }

    // Handle other status updates normally
    let dbStatus: string
    switch (status) {
      case "verified":
        dbStatus = "approuved" // Keep "approuved" with two 'p's as seen in the database
        break
      case "pending":
      default:
        dbStatus = "pending" // Keep as is
        break
    }

    console.log("Mapped status to:", dbStatus)

    // Prepare update data
    const updateData: any = {
      status: dbStatus,
      updated_at: new Date().toISOString(),
    }

    // Add status-specific fields
    if (status === "verified") {
      updateData.verified_at = new Date().toISOString()
      updateData.rejected_at = null
      updateData.rejection_reason = null
    } else {
      // Reset both if pending
      updateData.verified_at = null
      updateData.rejected_at = null
      updateData.rejection_reason = null
    }

    console.log("Update data:", updateData)

    // Use supabaseAdmin to bypass RLS policies
    const { data, error } = await supabaseAdmin.from("identity_verifications").update(updateData).eq("id", id).select()

    if (error) {
      console.error("Error updating verification:", error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    console.log("Update successful, returned data:", data)
    
    // Send email notification for approval
    if (status === "verified") {
      console.log("Attempting to send approval email notification")
      try {
        await sendVerificationEmail(data[0], "approved")
        console.log("Email notification sent successfully")
      } catch (emailError) {
        console.error("Failed to send email notification:", emailError)
      }
    }
    
    return NextResponse.json({ success: true, verification: data[0] })
  } catch (error) {
    console.error("Error in update verification API:", error)
    return NextResponse.json(
      {
        error: error instanceof Error ? error.message : "Internal server error",
      },
      { status: 500 },
    )
  }
}

// Helper function to send verification email
async function sendVerificationEmail(verification: any, emailStatus: "approved" | "rejected", rejectionReason?: string) {
  try {
    console.log("sendVerificationEmail called with:", { 
      verificationId: verification?.id,
      userId: verification?.user_id,
      emailStatus 
    })
    
    if (!verification || !verification.user_id) {
      console.error("Missing verification data for email")
      return
    }
    
    // Get user profile to get email and name
    console.log("Fetching user profile for ID:", verification.user_id)
    const { data: profile, error: profileError } = await supabaseAdmin
      .from("profiles")
      .select("email, fullname")
      .eq("id", verification.user_id)
      .single()
      
    if (profileError || !profile) {
      console.error("Error fetching user profile for email:", profileError)
      return
    }
    
    console.log("Found user profile:", profile)
    const userEmail = profile.email
    const userName = profile.fullname || "User"
    
    if (!userEmail) {
      console.error("No email found for user")
      return
    }
    
    // Check if user has opted in to receive system notifications
    console.log("Checking email preferences for user:", verification.user_id)
    const shouldSend = await shouldSendEmail(
      verification.user_id,
      "identity_verification"
    )
    
    console.log("Should send email based on preferences:", shouldSend)
    
    if (!shouldSend) {
      console.log(`User ${verification.user_id} has opted out of system notifications`)
      
      // Log skipped email
      const supabase = supabaseAdmin // Use admin client for this
      await logSkippedEmail(supabase, {
        user_id: verification.user_id,
        email: userEmail,
        email_type: "identity_verification",
        reference_id: verification.id
      })
      
      return
    }
    
    // Generate and send email
    console.log("Generating email content")
    const emailHtml = generateIdentityVerificationEmail({
      userName,
      status: emailStatus,
      rejectionReason: emailStatus === "rejected" ? rejectionReason : undefined,
      language: "fr" // Default to French
    })
    
    const subject = emailStatus === "approved"
      ? "Votre vérification d'identité a été approuvée"
      : "Votre vérification d'identité nécessite votre attention"
    
    console.log("Sending email to:", userEmail)  
    await emailService.sendEmail({
      to: userEmail,
      subject,
      html: emailHtml
    })
    
    console.log("Email sent successfully, logging to database")
    // Log successful email
    await supabaseAdmin
      .from("email_logs")
      .insert({
        email_type: "identity_verification",
        user_id: verification.user_id,
        email: userEmail,
        reference_id: verification.id,
        status: "success",
        sent_at: new Date().toISOString()
      })
      
    console.log(`Identity verification ${emailStatus} email sent successfully to ${userEmail}`)
  } catch (error) {
    console.error("Error sending verification email:", error)
    
    // Log failed email attempt if possible
    try {
      await supabaseAdmin
        .from("email_logs")
        .insert({
          email_type: "identity_verification",
          user_id: verification.user_id,
          email: verification.user_email || "unknown",
          reference_id: verification.id,
          status: "failed",
          error: error instanceof Error ? error.message : "Unknown error",
          sent_at: null
        })
    } catch (logError) {
      console.error("Error logging failed email:", logError)
    }
  }
}
