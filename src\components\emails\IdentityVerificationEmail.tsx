import { EmailTemplate } from './EmailTemplate';

interface IdentityVerificationEmailProps {
  userName: string;
  status: 'approved' | 'rejected';
  rejectionReason?: string;
  language?: 'en' | 'fr' | 'ar';
}

// Translations for the email content
const translations = {
  approved: {
    subject: {
      en: 'Your identity has been verified',
      fr: 'Votre identité a été validée',
      ar: 'تم التحقق من هويتك'
    },
    title: {
      en: 'Congratulations! Your identity has been verified successfully.',
      fr: 'Félicitations ! Votre identité a été validée avec succès.',
      ar: 'تهانينا! تم التحقق من هويتك بنجاح.'
    },
    message: {
      en: 'Congratulations! Your identity has been verified successfully. We thank you for your trust and ensure the security of your information. Do not hesitate to contact us for any questions or updates. Thank you for being part of our community!',
      fr: 'Félicitations ! Votre identité a été validée avec succès. Nous vous remercions pour la confiance accordée et assurons la sécurité de vos informations. N\'hésitez pas à nous contacter pour toute question ou mise à jour. Merci de faire partie de notre communauté !',
      ar: 'تهانينا! تم التحقق من هويتك بنجاح. نشكرك على ثقتك ونضمن أمان معلوماتك. لا تتردد في الاتصال بنا لأي أسئلة أو تحديثات. شكرًا لكونك جزءًا من مجتمعنا!'
    },
    cta: {
      en: 'View Profile',
      fr: 'Voir le Profil',
      ar: 'عرض الملف الشخصي'
    }
  },
  rejected: {
    subject: {
      en: 'Your identity verification request has been rejected',
      fr: 'Votre demande de vérification d\'identité n\'a pas été acceptée',
      ar: 'تم رفض طلب التحقق من هويتك'
    },
    title: {
      en: 'Unfortunately, your identity verification request has not been accepted.',
      fr: 'Malheureusement, votre demande de vérification d\'identité n\'a pas été acceptée.',
      ar: 'للأسف، لم يتم قبول طلب التحقق من هويتك.'
    },
    message: {
      en: 'We regret to inform you that your identity verification has been rejected. There seems to be an error in the information or documents provided, particularly some information that was not correctly filled in or complete. Please check your details and make sure everything is correct before submitting your request again. Make sure all required fields are filled in and that the documents are properly uploaded. If you have any questions, our support team is available to help you. Thank you for your understanding.',
      fr: 'Nous vous informons que votre vérification d\'identité a été refusée. Il semble y avoir une erreur dans les informations ou documents fournis, notamment certaines informations qui n\'ont pas été correctement remplies ou complètes. Veuillez vérifier vos coordonnées et vous assurer que tout est correct avant de soumettre à nouveau votre demande. Assurez-vous que tous les champs obligatoires sont remplis et que les documents sont bien téléchargés. Si vous avez des questions, notre équipe de support est disponible pour vous aider. Merci de votre compréhension.',
      ar: 'نأسف لإبلاغك بأنه تم رفض التحقق من هويتك. يبدو أن هناك خطأ في المعلومات أو المستندات المقدمة، خاصة بعض المعلومات التي لم يتم ملؤها أو إكمالها بشكل صحيح. يرجى التحقق من التفاصيل الخاصة بك والتأكد من صحة كل شيء قبل إرسال طلبك مرة أخرى. تأكد من ملء جميع الحقول المطلوبة وأن المستندات تم تحميلها بشكل صحيح. إذا كانت لديك أي أسئلة، فإن فريق الدعم لدينا متاح لمساعدتك. شكرا لتفهمك.'
    },
    reasonTitle: {
      en: 'Reason for rejection:',
      fr: 'Motif du refus:',
      ar: 'سبب الرفض:'
    },
    cta: {
      en: 'Complete Profile',
      fr: 'Compléter le profil',
      ar: 'إكمال الملف الشخصي'
    }
  }
};

export function generateIdentityVerificationEmail({
  userName,
  status,
  rejectionReason,
  language = 'fr'
}: IdentityVerificationEmailProps): string {
  const t = translations[status];
  const subject = t.subject[language] || t.subject.en;
  const title = t.title[language] || t.title.en;
  const message = t.message[language] || t.message.en;
  const cta = t.cta[language] || t.cta.en;
  
  // Use the production URL in production, localhost in development
  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';
  const profileUrl = `${baseUrl}/account`;
  
  // Logo and illustration URLs
  const logoUrl = 'https://api.almindharbooking.com/storage/v1/object/public/email-assets//listing_approval_email_logo.png';
  const illustrationUrl = status === 'approved' 
    ? 'https://api.almindharbooking.com/storage/v1/object/public/email-assets//identity-valid.png'
    : 'https://api.almindharbooking.com/storage/v1/object/public/email-assets//identity-not-valid.png';

  // Construct the email content
  let emailContent = '';
  
  if (status === 'approved') {
    emailContent = `
      <table width="100%" border="0" cellspacing="0" cellpadding="0" style="width: 100%; max-width: 100%; margin: 0; padding: 0;">
        <tr>
          <td align="center" valign="top">
            <!-- Header with logo -->
            <table width="100%" border="0" cellspacing="0" cellpadding="0" bgcolor="#F96F5D" style="background-color: #F96F5D; width: 100%; padding: 10px 0; border-bottom-right-radius: 40px;">
              <tr>
                <td align="center" valign="middle">
                  <img src="${logoUrl}" alt="Almindhar Booking" style="max-width: 150px; height: auto;" />
                </td>
              </tr>
            </table>
            
            <!-- Main content -->
            <table width="100%" border="0" cellspacing="0" cellpadding="0" style="width: 100%; padding: 10px 5px;">
              <tr>
                <td align="center" valign="top">
                  <h1 style="font-family: Arial, sans-serif; font-size: 24px; font-weight: bold; color: #333333; margin-bottom: 20px; text-align: center; width: 100%;">${title}</h1>
                  
                  <!-- Content container with light gray background -->
                  <table width="100%" border="0" cellspacing="0" cellpadding="0" style="width: 100%; background-color: rgba(217, 217, 217, 0.15); border-radius: 8px; margin-bottom: 15px;">
                    <tr>
                      <td style="padding: 20px 15px;">
                        <table width="100%" border="0" cellspacing="0" cellpadding="0">
                          <tr>
                            <td style="vertical-align: top; width: 75%;">
                              <p style="font-family: Arial, sans-serif; font-size: 16px; line-height: 1.5; color: #333333; margin-top: 0; text-align: left;">Bonjour ${userName},</p>
                              <p style="font-family: Arial, sans-serif; font-size: 16px; line-height: 1.5; color: #333333; text-align: left;">${message}</p>
                              <p style="font-family: Arial, sans-serif; font-size: 16px; line-height: 1.5; color: #333333; margin-top: 20px; text-align: left;"><strong>Cordialement,</strong></p>
                              <p style="font-family: Arial, sans-serif; font-size: 16px; line-height: 1.5; color: #333333; margin-bottom: 0; text-align: left;">L'équipe Almindhar Booking.</p>
                            </td>
                            <td style="vertical-align: top; width: 25%; text-align: right;">
                              <img src="${illustrationUrl}" alt="Identity Verified" style="width: 100px; height: auto; margin-top: -20px;" />
                            </td>
                          </tr>
                        </table>
                      </td>
                    </tr>
                  </table>
                  
                  <!-- CTA Button -->
                  <table border="0" cellspacing="0" cellpadding="0" style="margin: 0 auto 0 0;">
                    <tr>
                      <td align="left">
                        <table border="0" cellspacing="0" cellpadding="0">
                          <tr>
                            <td align="center" style="border-radius: 8px;" bgcolor="#F96F5D">
                              <a href="${profileUrl}" target="_blank" style="font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; color: #FFFFFF; text-decoration: none; border-radius: 8px; padding: 8px 25px; border: 1px solid #F96F5D; display: inline-block;">${cta}</a>
                            </td>
                          </tr>
                        </table>
                      </td>
                    </tr>
                  </table>
                </td>
              </tr>
            </table>
          </td>
        </tr>
      </table>
    `;
  } else {
    const reasonTitle = translations.rejected.reasonTitle[language] || translations.rejected.reasonTitle.en;
    
    emailContent = `
      <table width="100%" border="0" cellspacing="0" cellpadding="0" style="width: 100%; max-width: 100%; margin: 0; padding: 0;">
        <tr>
          <td align="center" valign="top">
            <!-- Header with logo -->
            <table width="100%" border="0" cellspacing="0" cellpadding="0" bgcolor="#F96F5D" style="background-color: #F96F5D; width: 100%; padding: 10px 0; border-bottom-right-radius: 40px;">
              <tr>
                <td align="center" valign="middle">
                  <img src="${logoUrl}" alt="Almindhar Booking" style="max-width: 150px; height: auto;" />
                </td>
              </tr>
            </table>
            
            <!-- Main content -->
            <table width="100%" border="0" cellspacing="0" cellpadding="0" style="width: 100%; padding: 10px 5px;">
              <tr>
                <td align="center" valign="top">
                  <h1 style="font-family: Arial, sans-serif; font-size: 24px; font-weight: bold; color: #333333; margin-bottom: 20px; text-align: center; width: 100%;">${title}</h1>
                  
                  <!-- Content container with light gray background -->
                  <table width="100%" border="0" cellspacing="0" cellpadding="0" style="width: 100%; background-color: rgba(217, 217, 217, 0.15); border-radius: 8px; margin-bottom: 15px;">
                    <tr>
                      <td style="padding: 20px 15px;">
                        <table width="100%" border="0" cellspacing="0" cellpadding="0">
                          <tr>
                            <td style="vertical-align: top; width: 75%;">
                              <p style="font-family: Arial, sans-serif; font-size: 16px; line-height: 1.5; color: #333333; margin-top: 0; text-align: left;">Bonjour ${userName},</p>
                              <p style="font-family: Arial, sans-serif; font-size: 16px; line-height: 1.5; color: #333333; text-align: left;">${message}</p>
                              
                              <!-- Rejection reason -->
                              <div style="background-color: #f8f8f8; border-left: 4px solid #F96F5D; margin: 20px 0; padding: 15px;">
                                <p style="font-family: Arial, sans-serif; font-size: 16px; font-weight: bold; color: #333333; margin-bottom: 5px; margin-top: 0;">${reasonTitle}</p>
                                <p style="font-family: Arial, sans-serif; font-size: 16px; color: #333333; margin: 0;">${rejectionReason || 'Vos documents n\'ont pas pu être vérifiés.'}</p>
                              </div>
                              
                              <p style="font-family: Arial, sans-serif; font-size: 16px; line-height: 1.5; color: #333333; margin-top: 20px; text-align: left;"><strong>Cordialement,</strong></p>
                              <p style="font-family: Arial, sans-serif; font-size: 16px; line-height: 1.5; color: #333333; margin-bottom: 0; text-align: left;">L'équipe Almindhar Booking.</p>
                            </td>
                            <td style="vertical-align: top; width: 25%; text-align: right;">
                              <img src="${illustrationUrl}" alt="Identity Verification Failed" style="width: 100px; height: auto; margin-top: -20px;" />
                            </td>
                          </tr>
                        </table>
                      </td>
                    </tr>
                  </table>
                  
                  <!-- CTA Button -->
                  <table border="0" cellspacing="0" cellpadding="0" style="margin: 0 auto 0 0;">
                    <tr>
                      <td align="left">
                        <table border="0" cellspacing="0" cellpadding="0">
                          <tr>
                            <td align="center" style="border-radius: 8px;" bgcolor="#F96F5D">
                              <a href="${profileUrl}" target="_blank" style="font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; color: #FFFFFF; text-decoration: none; border-radius: 8px; padding: 8px 25px; border: 1px solid #F96F5D; display: inline-block;">${cta}</a>
                            </td>
                          </tr>
                        </table>
                      </td>
                    </tr>
                  </table>
                </td>
              </tr>
            </table>
          </td>
        </tr>
      </table>
    `;
  }

  // Wrap the content in the EmailTemplate component
  return `
    <!DOCTYPE html>
    <html>
      <head>
        <meta charset="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>${subject}</title>
      </head>
      <body style="margin: 0; padding: 0; background-color: #ffffff; font-family: Arial, sans-serif;">
        ${emailContent}
        <div style="text-align: center; padding: 20px; color: #666; font-size: 12px;">
          2024 Almindhar Booking. All rights reserved.
        </div>
      </body>
    </html>
  `;
}
