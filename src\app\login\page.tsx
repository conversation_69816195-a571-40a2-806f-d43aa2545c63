'use client';

import React, { useState, Suspense, useEffect } from 'react';
import facebookSvg from '@/images/Facebook.svg';
import googleSvg from '@/images/Google.svg';
import Input from '@/shared/Input';
import Image from 'next/image';
import Link from 'next/link';
import { login } from '@/app/auth/action';
import { createClient } from '@/utils/supabase/client';
import { useRouter, useSearchParams } from 'next/navigation';
import { useUser } from '@/contexts/UserContext';
const supabase = createClient();

const handleGoogleLogin = async () => {
  try {
    const { error } = await supabase.auth.signInWithOAuth({
      provider: 'google',
      options: {
        redirectTo: `${window.location.origin}/auth/callback`,
        queryParams: {
          access_type: 'offline',
          prompt: 'consent'
        }
      }
    });

    if (error) {
      console.error('Google sign in error:', error);
    }
  } catch (error) {
    console.error('OAuth error:', error);
  }
};

const loginSocials = [

  {
    name: 'Continuer avec Google',
    icon: googleSvg,
    action: handleGoogleLogin
  }
];

const PageLogin = () => {
  const router = useRouter();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const searchParams = useSearchParams();
  const redirect = searchParams?.get('redirect') || '/';
  const { refreshUserProfile } = useUser();

  useEffect(() => {
    const checkAuth = async () => {
      const { data: { session } } = await supabase.auth.getSession();
      if (session) {
        router.push(redirect);
      }
    };

    // Initial check
    checkAuth();

    // Listen for auth state changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange((event, session) => {
      if (session?.user) {
        router.push(redirect);
      }
    });

    // Cleanup subscription
    return () => {
      subscription.unsubscribe();
    };
  }, [redirect, router]);

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setIsLoading(true);

    try {
        const formData = new FormData();
        formData.append('email', email);
        formData.append('password', password);
        formData.append('redirect', redirect);

        const result = await login(formData);

        if (result.error) {
            setError(result.error);
        } else if (result.redirect) {
            // Navigate to the redirect URL
            router.push(result.redirect);
        } else {
            // Add a small delay to allow auth state to propagate
            await new Promise(resolve => setTimeout(resolve, 500));
            
            // Force a refresh of the user profile
            await refreshUserProfile();
            
            // Navigate to home page
            router.push('/');
        }
    } catch (error) {
        setError('An unexpected error occurred, Please try again.');
        console.error('Login error:', error);
    } finally {
        setIsLoading(false);
    }
};

  return (
    <div className="nc-PageLogin">
      <div className="container mb-24 lg:mb-32">
        <h2 className="text-3xl font-semibold text-center my-20">Connexion</h2>
        <div className="max-w-md mx-auto space-y-6">
          <div className="grid gap-3">
            {loginSocials.map((item, index) => (
              <button
                key={index}
                onClick={item.action}
                className="flex items-center w-full rounded-lg bg-light-orange px-4 py-3 hover:translate-y-[-2px] transition-transform disabled:opacity-70"
                disabled={isLoading}
              >
                <Image src={item.icon} alt={item.name} />
                <h3 className="flex-grow text-center">{item.name}</h3>
              </button>
            ))}
          </div>

          <div className="relative text-center">
            <span className="relative z-10 inline-block px-4 font-medium text-sm bg-white dark:bg-neutral-900">
              OU
            </span>
            <div className="absolute left-0 w-full top-1/2 transform -translate-y-1/2 border border-neutral-100 dark:border-neutral-800"></div>
          </div>

          <form onSubmit={handleLogin} className="grid gap-6">
            {error && (
              <div className="text-red-600 bg-red-50 p-3 rounded-lg text-sm">
                {error}
              </div>
            )}
            <label className="block">
              <span className="text-neutral-800 dark:text-neutral-200">
                Adresse e-mail
              </span>
              <Input
                type="email"
                placeholder="<EMAIL>"
                className="mt-1"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
                disabled={isLoading}
              />
            </label>
            <label className="block">
              <span className="text-neutral-800 dark:text-neutral-200">
                Mot de passe
              </span>
              <Input
                type="password"
                className="mt-1"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                required
                disabled={isLoading}
              />
            </label>
            <button
              type="submit"
              className="w-full bg-booking-orange text-white py-2 rounded-lg hover:bg-booking-orange transition-colors disabled:opacity-70"
              disabled={isLoading}
            >
              {isLoading ? 'Connexion en cours...' : 'Continuer'}
            </button>
          </form>

          <div className="text-center space-y-2">
            <Link
              href="/signup"
              className="text-booking-orange hover:underline dark:text-blue-500"
            >
              Nouveau utilisateur ? Créer un compte
            </Link>
            <Link
              href="/forgot-password"
              className="block text-sm text-neutral-700 hover:underline dark:text-neutral-300"
            >
              Mot de passe oublié ?
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

const LoginPageWithSuspense = () => (
  <Suspense fallback={<div>Loading...</div>}>
    <PageLogin />
  </Suspense>
);

export default LoginPageWithSuspense;
