"use client"

import type React from "react"
import { useEffect, useState, useCallback } from "react"
import { useToast } from "@/hooks/use-toast"
import { cn } from "@/lib/utils"
import Image from "next/image"
import { useUser } from "@/contexts/UserContext"
import { useAvatarUpload } from "@/hooks/useAvatarUpload"
import { useRouter } from "next/navigation"
import Modal from "@/components/Modal"
import { VerificationBadge } from "@/components/verification-badge"
import { getCachedImageUrl } from "@/utils/imageLoader"

const AccountPage = () => {
  const router = useRouter()
  const { toast } = useToast()
  const { userProfile, updateUserProfile, refreshUserProfile } = useUser()
  const { uploadAvatar, isUploading } = useAvatarUpload()

  // State management
  const [isImageLoading, setIsImageLoading] = useState(true)
  const [imageError, setImageError] = useState(false)
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false)
  const [isDeletingAccount, setIsDeletingAccount] = useState(false)
  const [submitting, setSubmitting] = useState(false)
  const [isPageLoading, setIsPageLoading] = useState(true)
  const [verificationStatus, setVerificationStatus] = useState<"pending" | "verified" | "rejected" | "not_submitted">(
      "not_submitted",
  )

  // Initialize profile state with user data or defaults
  const [profile, setProfile] = useState({
    fullname: userProfile?.fullname ?? "",
    email: userProfile?.email ?? "",
    avatar_url: userProfile?.avatar_url ?? "",
    bio: userProfile?.bio ?? "",
    phone_number: userProfile?.phone_number ?? "",
  })

  // Computed loading state
  const isLoading = submitting || isPageLoading || !userProfile

  /**
   * Validates full name input
   */
  const validateFullName = (name: string) => {
    if (name.length < 2) return "Le nom doit contenir au moins 2 caractères"
    if (name.length > 50) return "Le nom ne peut pas dépasser 50 caractères"
    if (!/^[a-zA-ZÀ-ÿ\s'-]+$/.test(name)) return "Le nom contient des caractères non autorisés"
    return null
  }

  /**
   * Updates the profile state with data from the userProfile context
   */
  const updateProfileFromContext = useCallback(() => {
    if (userProfile) {
      setProfile({
        fullname: userProfile.fullname || "",
        email: userProfile.email || "",
        avatar_url: userProfile.avatar_url || "",
        bio: userProfile.bio || "",
        phone_number: userProfile.phone_number || "",
      })
    }
  }, [userProfile])

  /**
   * Updates the user's profile via the API
   */
  const handleUpdateProfile = useCallback(
      async (newFullname: string, newBio: string, newPhoneNumber: string) => {
        try {
          setSubmitting(true)

          // Update the user context with new values (which will trigger a React Query mutation)
          await updateUserProfile({
            fullname: newFullname,
            bio: newBio,
            phone_number: newPhoneNumber,
          })

          // Update the local state
          setProfile((prev) => ({
            ...prev,
            fullname: newFullname,
            bio: newBio,
            phone_number: newPhoneNumber,
          }))

          toast({
            title: "Succès",
            description: "Votre profil a été mis à jour avec succès.",
          })
        } catch (error: any) {
          console.error("Error updating profile:", error)
          toast({
            title: "Erreur",
            description: error.message || "Une erreur est survenue lors de la mise à jour du profil.",
            variant: "destructive",
          })
        } finally {
          setSubmitting(false)
        }
      },
      [updateUserProfile, toast],
  )

  /**
   * Check if the user already has a verification
   */
  const checkVerificationStatus = useCallback(async (userId: string) => {
    try {
      const response = await fetch("/api/profile/verification")

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Failed to check verification status")
      }

      const data = await response.json()
      setVerificationStatus(data.status)
    } catch (error) {
      console.error("Error checking verification status:", error)
      setVerificationStatus("not_submitted")
    }
  }, [])

  /**
   * Handles the account deletion process
   */
  const handleDeleteAccount = async () => {
    try {
      setIsDeletingAccount(true)

      // This operation still needs a direct API call since it's a specific action
      const response = await fetch("/api/profile", {
        method: "DELETE",
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Failed to delete account")
      }

      toast({
        title: "Compte supprimé",
        description: "Votre compte a été supprimé avec succès.",
      })

      router.push("/")
    } catch (error: any) {
      console.error("Error during account deletion:", error)
      toast({
        title: "Erreur",
        description: error.message || "Une erreur est survenue lors de la suppression du compte.",
        variant: "destructive",
      })
    } finally {
      setIsDeletingAccount(false)
      setIsDeleteModalOpen(false)
    }
  }

  /**
   * Handles avatar file upload with validation
   */
  const handleAvatarUpload = useCallback(
      async (event: React.ChangeEvent<HTMLInputElement>) => {
        try {
          const file = event.target.files?.[0]
          if (!file) return

          // Use the React Query-based avatar upload hook
          uploadAvatar(file, {
            onSuccess: (data) => {
              // Update local state with new avatar URL and apply caching
              const cachedAvatarUrl = getCachedImageUrl(data.url) || data.url

              setProfile((prev) => ({
                ...prev,
                avatar_url: cachedAvatarUrl as string,
              }))

              toast({
                title: "Avatar mis à jour",
                description: "Votre photo de profil a été mise à jour avec succès.",
              })
            },
            onError: (error) => {
              console.error("Error uploading avatar:", error)
              toast({
                title: "Erreur",
                description: error.message || "Une erreur est survenue lors du téléchargement de l'avatar.",
                variant: "destructive",
              })
            },
          })
        } catch (error: any) {
          console.error("Error validating avatar:", error)
          toast({
            title: "Erreur",
            description: error.message || "Une erreur est survenue lors de la validation de l'avatar.",
            variant: "destructive",
          })
        }
      },
      [toast, uploadAvatar],
  )

  // Initialize the page with user profile data
  const initializeProfile = useCallback(async () => {
    try {
      setIsPageLoading(true)

      // Update profile state from the UserContext
      updateProfileFromContext()

      // If we have a user ID, check verification status
      if (userProfile?.id) {
        await checkVerificationStatus(userProfile.id)
      }
    } catch (error) {
      console.error("Error initializing profile:", error)
    } finally {
      setIsPageLoading(false)
    }
  }, [userProfile, checkVerificationStatus, updateProfileFromContext])

  // Initialize profile data when component mounts or userProfile changes
  useEffect(() => {
    initializeProfile()
  }, [initializeProfile])

  // Show loading state if data is not ready
  if (isLoading) {
    return (
        <div className="min-h-screen flex items-center justify-center">
          <div className="space-y-8 flex flex-col items-center">
            <div className="w-20 h-20 rounded-full bg-neutral-100 animate-pulse" />
            <p className="text-neutral-500">Chargement de votre profil...</p>
          </div>
        </div>
    )
  }

  // Main component render
  return (
      <div className="p-8">
        <h2 className="text-2xl font-medium text-black mb-8">informations personnelles</h2>

        <div className="max-w-2xl">
          <div className="mb-8">
            <h3 className="text-base font-medium mb-2">Avatar</h3>
            <div className="flex items-center gap-4">
              <div className="relative w-16 h-16">
                <div className="w-full h-full rounded-full overflow-hidden border border-gray-200">
                  {profile.avatar_url && !imageError ? (
                      <div className="relative w-full h-full">
                        {isImageLoading && <div className="absolute inset-0 bg-gray-200 animate-pulse" />}
                        <Image
                            width={200}
                            height={200}
                            src={profile.avatar_url || "/placeholder.svg"}
                            alt="Photo de profil"
                            className={cn(
                                "w-full h-full object-cover transition-opacity duration-300",
                                isImageLoading ? "opacity-0" : "opacity-100",
                            )}
                            onLoad={() => setIsImageLoading(false)}
                            onError={() => {
                              setImageError(true)
                              setIsImageLoading(false)
                            }}
                            priority
                        />
                      </div>
                  ) : (
                      <div className="w-full h-full bg-gray-100 flex items-center justify-center text-center p-2">
                        <span className="text-gray-400 text-xs">{imageError ? "Erreur" : "Photo"}</span>
                      </div>
                  )}
                </div>

                {/* Add verification badge if user is verified */}
                {verificationStatus === "verified" && (
                    <div className="absolute -bottom-1 -right-1">
                      <VerificationBadge verified={true} size="sm" />
                    </div>
                )}
              </div>
              <div>
                <label className="text-sm text-gray-700 cursor-pointer hover:text-black">
                  <span className="underline">Changer l&apos;avatar</span>
                  <input
                      type="file"
                      className="hidden"
                      accept="image/*"
                      onChange={handleAvatarUpload}
                      disabled={isLoading}
                      aria-busy={isLoading}
                  />
                </label>
                <p className="text-xs text-gray-500 mt-1">Min 200x200px, PNG or JPEG</p>
              </div>
            </div>
          </div>

          <form
              onSubmit={(e) => {
                e.preventDefault()
                handleUpdateProfile(profile.fullname, profile.bio, profile.phone_number)
              }}
              className="space-y-6"
          >
            <div>
              <label htmlFor="fullname" className="block text-sm font-medium mb-1">
                Nom complet
              </label>
              <input
                  id="fullname"
                  type="text"
                  value={profile.fullname}
                  onChange={(e) => setProfile((prev) => ({ ...prev, fullname: e.target.value }))}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-black"
              />
            </div>

            <div>
              <label htmlFor="bio" className="block text-sm font-medium mb-1">
                Bio
              </label>
              <textarea
                  id="bio"
                  value={profile.bio}
                  onChange={(e) => setProfile((prev) => ({ ...prev, bio: e.target.value }))}
                  rows={4}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-black"
              />
            </div>

            <div>
              <label htmlFor="email" className="block text-sm font-medium mb-1">
                Adresse e-mail
              </label>
              <input
                  id="email"
                  type="email"
                  value={profile.email}
                  disabled
                  className="w-full border border-gray-300 rounded-md px-3 py-2 bg-gray-50 cursor-not-allowed"
              />
            </div>

            <div>
              <label htmlFor="phone" className="block text-sm font-medium mb-1">
                Numéro de téléphone
              </label>
              <input
                  id="phone"
                  type="tel"
                  value={profile.phone_number}
                  onChange={(e) => setProfile((prev) => ({ ...prev, phone_number: e.target.value }))}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-black"
              />
            </div>

            <div className="pt-4">
              <button
                  type="submit"
                  disabled={isLoading}
                  className="bg-black text-white px-4 py-2 rounded-md hover:bg-gray-800 transition-colors"
              >
                Enregistrer les modifications
              </button>
            </div>
          </form>

          <div className="mt-12 pt-6 border-t border-gray-200">
            <button className="text-red-600 hover:text-red-700 font-medium" onClick={() => setIsDeleteModalOpen(true)}>
              Supprimer mon compte
            </button>
          </div>
        </div>

        <Modal
            isOpen={isDeleteModalOpen}
            onClose={() => setIsDeleteModalOpen(false)}
            onConfirm={handleDeleteAccount}
            title="Supprimer le compte"
            message="Êtes-vous sûr de vouloir supprimer votre compte ? Cette action est irréversible et toutes vos données seront définitivement supprimées."
            isLoading={isDeletingAccount}
        />
      </div>
  )
}

export default AccountPage
