{"datePicker": {"previousMonth": "<PERSON><PERSON>", "nextMonth": "<PERSON><PERSON>", "months": {"long": "long", "numeric": "numérique"}}, "cardCategory": {"minutesDrive": "minutes en voiture"}, "collection": {"showMore": "Voir plus"}, "creditCardForm": {"cardNumber": "<PERSON><PERSON><PERSON><PERSON>", "cardName": "Nom sur la Carte", "expirationDate": "Date d'Expiration", "month": "<PERSON><PERSON>", "year": "<PERSON><PERSON>", "cvv": "CVV", "cvvInfo": "Le numéro CVV est un code de sécurité à 3 chiffres", "submit": "So<PERSON><PERSON><PERSON>", "cancel": "Annuler"}, "commentListing": {"reviewIn": "avis dans"}, "common": {"visa": "VISA", "mastercard": "MASTERCARD", "hoursShort": "h", "minutesShort": "m", "next": "Suivant", "previous": "Précédent", "email": "Email", "password": "Mot de passe", "confirmPassword": "Confirmer le mot de passe", "name": "Nom", "firstName": "Prénom", "lastName": "Nom de famille", "submit": "So<PERSON><PERSON><PERSON>", "login": "Connexion", "register": "S'inscrire", "logout": "Déconnexion", "search": "<PERSON><PERSON><PERSON>", "loading": "Chargement...", "error": "Une erreur s'est produite", "success": "Succès !", "cancel": "Annuler", "save": "Enregistrer", "edit": "Modifier", "delete": "<PERSON><PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "Mettre à jour"}, "experiencesCard": {"person": "personne", "ads": "PUB"}, "editListingModal": {"editListing": "Modifier l'annonce", "title": "Titre", "description": "Description", "propertyType": "Type de propriété", "selectPropertyType": "Sélectionner le type de propriété", "city": "Ville", "state": "État", "nightlyRate": "Tarif par nuit", "placeType": "Type de lieu", "selectPlaceType": "Sélectionner le type de lieu", "paymentType": "Type de paiement", "selectPaymentType": "Sélectionner le type de paiement", "saveChanges": "Enregistrer les modifications", "propertyTypes": {"villa": "Villa", "house": "<PERSON><PERSON>", "apartment": "Appartement", "cabin": "<PERSON><PERSON>"}, "placeTypes": {"entirePlace": "Logement entier", "privateRoom": "Chambre privée", "sharedRoom": "Chambre partagée"}, "paymentTypes": {"instantBooking": "Réservation instantanée", "cashBased": "Paiement en espèces"}}, "flightCard": {"tripTime": "<PERSON><PERSON><PERSON> du voyage", "transitTime": "Temps de transit", "nonstop": "Sans escale", "roundTrip": "aller-retour"}, "footer": {"gettingStarted": "Commencer", "installation": "Installation", "releaseNotes": "Notes de version", "upgradeGuide": "Guide de mise à niveau", "browserSupport": "Support de navigateur", "editorSupport": "Support d'éditeur", "explore": "Explorer", "designFeatures": "Fonctionnalités de design", "prototyping": "Prototypage", "designSystems": "Systèmes de design", "pricing": "Tarification", "security": "Sécurité", "resources": "Ressources", "bestPractices": "Meilleures pratiques", "support": "Support", "developers": "Développeurs", "learnDesign": "Apprendre le design", "releases": "Versions", "community": "Communauté", "discussionForums": "Forums de discussion", "codeOfConduct": "Code de conduite", "communityResources": "Ressources communautaires", "contributing": "Contribuer", "concurrentMode": "Mode concurrent"}, "footerNav": {"explore": "Explorer", "wishlists": "<PERSON><PERSON><PERSON>", "login": "Connexion", "menu": "<PERSON><PERSON>"}, "headerFilter": {"latestArticles": "Derniers Articles 🎈", "viewAll": "Voir tout"}, "invoiceDetails": {"invoice": "Facture", "status": "Statut", "billingFrom": "Facturation de", "paymentDate": "Date de paiement", "paymentFrom": "Paiement de", "paymentTo": "Paiement à", "propertyDetails": "Détails de la propriété", "invoiceDate": "Date de facturation", "dueDate": "Date d'échéance", "priceDetails": "Détails du prix", "basePrice": "Prix de base", "serviceFee": "Frais de service", "vatServiceFee": "TVA sur les frais de service", "total": "Total", "securityFees": "Frais de sécurité", "downloadPdf": "Télécharger le PDF", "completed": "<PERSON><PERSON><PERSON><PERSON>"}, "invoiceTable": {"invoice": "Facture", "billingFrom": "Facturation de", "status": "Statut", "paymentDate": "Date de paiement", "listings": "Annonces", "amount": "<PERSON><PERSON>", "action": "Action", "noResults": "Aucun résultat.", "previous": "Précédent", "next": "Suivant", "invoiceDetails": "<PERSON>é<PERSON> de la facture"}, "label": {"default": "Étiquette"}, "messageHost": {"typeMessage": "Tapez votre message ici...", "sendMessage": "Envoyer le message"}, "messageList": {"loadingMessages": "Chargement des messages..."}, "mapContainer": {"searchAsIMove": "Rechercher pendant que je déplace la carte"}, "messageForm": {"typeMessage": "Tapez un message...", "send": "Envoyer"}, "likeSaveBtns": {"share": "Partager", "save": "<PERSON><PERSON><PERSON><PERSON>"}, "modalSelectGuests": {"selectGuests": "Sélectionner les invités", "clear": "<PERSON><PERSON><PERSON><PERSON>", "save": "Enregistrer"}, "modalSelectDate": {"selectDate": "Sélectionner une date", "whenIsYourTrip": "Quand est votre voyage ?", "clearDates": "Effacer les dates", "save": "Enregistrer"}, "ncInputNumber": {"defaultLabel": "Nombre"}, "newStayCard": {"night": "nuit"}, "nextBtn": {"next": "Suivant"}, "occupancyChart": {"occupancyAnalysis": "Analyse d'occupation", "weekly": "Hebdomadaire", "monthly": "<PERSON><PERSON><PERSON>", "annually": "<PERSON><PERSON>", "houseAnalysis": "<PERSON><PERSON><PERSON> de la maison", "guestSegmentation": "Segmentation des invités", "stayDuration": "<PERSON><PERSON><PERSON> du s<PERSON>jour", "seasonAnalysis": "<PERSON><PERSON><PERSON>", "statistics": "Statistiques", "viewsByCountry": "Vues par pays", "activeUsers": "Utilisateurs actifs", "vsLastWeek": "VS SEMAINE DERNIÈRE"}, "overviewStats": {"activeBookings": "Réservations actives", "totalBookings": "Total des réservations", "cancellations": "Annulations", "totalRevenue": "Revenu total"}, "paymentMethodCard": {"copyInfo": "<PERSON><PERSON><PERSON> les informations", "account": "<PERSON><PERSON><PERSON>", "number": "<PERSON><PERSON><PERSON><PERSON>", "expiryDate": "Date d'expiration", "routing": "<PERSON><PERSON><PERSON> (ABA)", "accountNumber": "Numéro de compte", "currentBalance": "Solde actuel", "withdraw": "<PERSON><PERSON><PERSON>", "share": "Partager", "delete": "<PERSON><PERSON><PERSON><PERSON>"}, "paymentStatusChart": {"paymentStatus": "Statut de paiement", "pendingPayment": "Paiement en attente", "completedPayment": "Paiement effectué", "failedPayment": "<PERSON><PERSON><PERSON>", "refundedAmount": "<PERSON><PERSON>"}, "paymentStatusCharts": {"total": "Total", "completedPayment": "Paiement effectué", "pendingPayment": "Paiement en attente", "failedPayment": "<PERSON><PERSON><PERSON>", "refundedAmount": "<PERSON><PERSON>", "noDataAvailable": "<PERSON><PERSON><PERSON> donnée disponible"}, "postCardMeta": {"readMore": "Lire la suite"}, "sectionBecomeAnAuthor": {"whyChooseUs": "Pourquoi nous avez-vous choisi ?", "description": "En nous accompagnant, vous vivez un voyage rempli d'expériences. <PERSON><PERSON>, la réservation d'hébergements, de villas de resort, d'hôtels, de maisons privées, d'appartements... devient rapide, pratique et facile.", "becomeAnAuthor": "<PERSON><PERSON><PERSON> un auteur"}, "sectionClientSay": {"goodNewsFromFarAway": "Bonnes nouvelles de loin", "clientThoughts": "Voyons ce que les gens pensent de Chisfis"}, "sectionGridAuthorBox": {"top10Authors": "Top 10 des auteurs du mois", "ratingDescription": "Évaluation basée sur les avis des clients", "showMore": "Montrez-moi plus", "becomeAHost": "<PERSON><PERSON><PERSON> un hôte"}, "saleOffBadge": {"saleOff": "-10% aujourd'hui"}, "recentActivities": {"title": "Activités Récentes", "viewMore": "Voir Plus"}, "propertyCardH": {"network": "<PERSON><PERSON><PERSON>", "family": "<PERSON><PERSON><PERSON>", "beds": "lits", "baths": "salles de bain", "sqFt": "m²"}, "sectionGridCategoryBox": {"exploreNearby": "Explorer les environs", "discoverGreatPlaces": "Découvrez de superbes endroits près de chez vous"}, "sectionSubscribe2": {"title": "Rejoignez notre newsletter 🎉", "description": "Lisez et partagez de nouvelles perspectives sur presque tous les sujets. Tout le monde est le bienvenu.", "getMoreDiscount": "Obtenez plus de réductions", "getPremiumMagazines": "Obtenez des magazines premium", "enterYourEmail": "Entrez votre email"}, "sideNav": {"dashboard": "Tableau de bord", "bookings": "Réservations", "myProperties": "Mes proprié<PERSON>", "listingManagement": "Gestion des annonces", "calendar": "<PERSON><PERSON><PERSON>", "guestManagement": "Gestion des invités", "financials": "Finances", "invoice": "Facture", "paymentInfo": "Informations de paiement", "paymentStatus": "Statut de paiement", "help": "Aide", "settings": "Paramètres"}, "sectionGridFeaturePlaces": {"featuredPlacesToStay": "<PERSON><PERSON> de séjour en vedette", "popularPlaces": "Lieux de séjour populaires recommandés par Chisfis", "showMeMore": "Montrez-moi plus"}, "sectionHowItWork": {"howItWork": "Comment ça marche", "keepCalmTravel": "Restez calme et voyagez", "bookAndRelax": "Réservez et détendez-vous", "smartChecklist": "Liste de contrôle intelligente", "saveMore": "Économisez plus", "enjoyTrip": "Que chaque voyage soit un voyage inspirant, chaque chambre un espace paisible"}, "sectionOurFeatures": {"benefits": "Avantages", "happeningCities": "Villes animées", "costEffectiveAdvertising": "Publicité rentable", "advertisingDescription": "Avec une annonce gratuite, vous pouvez faire de la publicité pour votre location sans frais initiaux", "reachMillions": "Atteignez des millions avec Chisfis", "reachDescription": "Des millions de personnes recherchent des endroits uniques où séjourner dans le monde entier", "secureAndSimple": "Sécurisé et simple", "secureDescription": "Une annonce Holiday Lettings vous offre un moyen sûr et facile de prendre des réservations et des paiements en ligne"}, "statsCard": {"date": "Date", "value": "<PERSON><PERSON>"}, "stayCard": {"night": "nuit"}, "sectionVideos": {"theVideos": "🎬 Les Vidéos", "videosDescription": "Découvrez nos vidéos les plus populaires. Regardez plus et partagez plus de nouvelles perspectives sur presque tous les sujets. Tout le monde est le bienvenu."}, "sectionSliderNewCategories": {"suggestionsForDiscovery": "Suggestions pour la découverte", "popularPlaces": "Lieux populaires recommandés pour vous"}, "startRating": {"reviews": "avis"}, "tableOfContents": {"inThisArticle": "Dans cet article"}, "stayCardH": {"guests": "invités", "beds": "lits", "baths": "salles de bain", "noSmoking": "Non-fumeur", "bedrooms": "chambres", "wifi": "Wifi", "night": "nuit"}, "userNav": {"profile": "Profil", "settings": "Paramètres", "logOut": "Se déconnecter"}, "stayCard2": {"beds": "lits", "night": "nuit"}, "nextPrev": {"prev": "Précédent", "next": "Suivant"}, "pagination": {"page": "Page"}, "tag": {"count": "nombre"}, "socialsShare": {"shareOn": "Partager sur"}, "socialsList": {"facebook": "Facebook", "twitter": "Twitter", "youtube": "Youtube", "instagram": "Instagram"}, "switchDarkMode": {"enableDarkMode": "Activer le mode sombre"}, "buttonThird": {"button": "Bouton"}, "heading": {"sectionHeading": "Titre de section", "discoverOutstandingArticles": "Découvrez les articles les plus remarquables sur tous les sujets de la vie."}, "comment": {"reply": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "heading2": {"staysInTokyo": "Séjours à Tokyo", "staysInfo": "233 séjours · 12-18 août · 2 invités"}, "fileUploader": {"uploadFile": "Télécharger un fichier", "dragAndDrop": "ou glisser-déposer", "fileSizeLimit": "PNG, JPG, GIF jusqu'à 10 Mo", "fileTooLarge": "Le fichier est trop volumineux (maximum 10 Mo)."}, "buttonClose": {"close": "<PERSON><PERSON><PERSON>"}, "navigation": {"home": "Accueil", "about": "À propos", "contact": "Contact", "blog": "Blog", "profile": "Profil", "settings": "Paramètres"}, "auth": {"loginTitle": "Connectez-vous à votre compte", "registerTitle": "<PERSON><PERSON>er un nouveau compte", "forgotPassword": "Mot de passe oublié ?", "rememberMe": "Se souvenir de moi", "dontHaveAccount": "Vous n'avez pas de compte ?", "alreadyHaveAccount": "Vous avez déjà un compte ?"}, "profile": {"personalInfo": "Informations personnelles", "updateProfile": "Mettre à jour le profil", "changePassword": "Changer le mot de passe", "deleteAccount": "Supprimer le compte"}, "errors": {"required": "Ce champ est obligatoire", "invalidEmail": "Veu<PERSON>z entrer une adresse email valide", "passwordMismatch": "Les mots de passe ne correspondent pas", "weakPassword": "Le mot de passe est trop faible", "userNotFound": "Utilisateur non trouvé", "incorrectPassword": "Mot de passe incorrect", "emailAlreadyInUse": "Cet email est déjà utilisé"}, "addListing": {"step1": {"title": "Étape 1 : Informations de base", "description": "Commençons par les bases de votre logement.", "propertyType": "Quel type de logement proposez-vous ?", "propertyTypeOptions": {"apartment": "Appartement", "house": "<PERSON><PERSON>", "secondaryUnit": "Logement secondaire", "uniqueSpace": "Espace unique", "bedAndBreakfast": "Bed and breakfast", "boutiqueHotel": "Hôtel boutique"}, "listingType": "Quel type d'annonce est-ce ?", "listingTypeOptions": {"entirePlace": "Un logement entier", "privateRoom": "Une chambre privée", "sharedRoom": "Une chambre partagée"}, "location": "Où se trouve votre logement ?", "locationPlaceholder": "Entrez votre adresse"}, "step2": {"title": "Étape 2 : Chambres et espaces", "description": "Informez les voyageurs sur les chambres et les espaces de votre logement.", "guestCount": "Combien de voyageurs votre logement peut-il accueillir ?", "bedroomCount": "Combien de chambres sont disponibles pour les voyageurs ?", "bedCount": "Combien de lits les voyageurs peuvent-ils utiliser ?", "bathroomCount": "Combien de salles de bain sont disponibles pour les voyageurs ?"}, "step3": {"title": "Étape 3 : Équipements", "description": "Faites savoir aux voyageurs ce que votre logement a à offrir.", "amenities": "Quels équipements proposez-vous ?", "amenityOptions": {"wifi": "Wifi", "tv": "TV", "kitchen": "<PERSON><PERSON><PERSON><PERSON>", "washer": "Lave-linge", "freeParking": "Parking gratuit", "paidParking": "Parking payant", "airConditioning": "Climatisation", "dedicatedWorkspace": "Espace de travail dédié", "pool": "Piscine", "hotTub": "<PERSON><PERSON><PERSON><PERSON>", "patio": "Terrasse", "bbqGrill": "Barbecue", "outdoorDiningArea": "Espace repas extérieur", "firepit": "<PERSON><PERSON><PERSON> extérieur", "gym": "Salle de sport", "beachAccess": "Accès à la plage", "lakeAccess": "Accès au lac", "skiInSkiOut": "Ski aux pieds", "outdoorShower": "Douche extérieure"}}, "step4": {"title": "Étape 4 : Photos", "description": "Ajoutez quelques photos de votre logement.", "photoUpload": "Télécharger des photos", "photoUploadDescription": "Faites glisser et déposez vos photos ici, ou cliquez pour sélectionner des fichiers", "photoRequirements": "Les photos doivent avoir une taille d'au moins 1024x683 pixels et être au format JPG ou PNG"}, "step5": {"title": "Étape 5 : Titre et description", "description": "<PERSON><PERSON>ez un titre et une description pour votre annonce.", "listingTitle": "Titre de l'annonce", "listingTitlePlaceholder": "Entrez un titre accrocheur pour votre annonce", "listingDescription": "Description de l'annonce", "listingDescriptionPlaceholder": "Décrivez votre logement aux voyageurs potentiels"}, "step6": {"title": "Étape 6 : Prix", "description": "Définissez un prix pour votre annonce.", "basePrice": "Prix de base (par nuit)", "cleaningFee": "Frais de ménage", "additionalGuestFee": "Frais pour voyageur supplémentaire", "securityDeposit": "Caution"}, "step7": {"title": "Étape 7 : Paramètres de réservation", "description": "Choisissez vos paramètres de réservation.", "instantBook": "Autoriser la réservation instantanée", "minNights": "Séjour minimum (nuits)", "maxNights": "Séjour maximum (nuits)", "checkInTime": "<PERSON>ure d'arrivée", "checkOutTime": "<PERSON><PERSON>"}, "step8": {"title": "Étape 8 : R<PERSON>glement intérieur", "description": "Définissez les règles pour vos voyageurs.", "rules": "Règlement intérieur", "ruleOptions": {"noSmoking": "Non-fumeur", "noPets": "<PERSON><PERSON> d'animaux", "noParties": "Pas de fête ni de soirée", "noUnregisteredGuests": "Pas de voyageurs non enregistrés", "quietHours": "Heures de silence", "checkInTime": "<PERSON>ure d'arrivée", "checkOutTime": "<PERSON><PERSON>"}, "customRules": "Règles supplémentaires (facultatif)"}, "step9": {"title": "Étape 9 : Disponibilité", "description": "Définissez la disponibilité de votre annonce.", "availabilityType": "Comment voulez-vous définir votre disponibilité ?", "availabilityTypeOptions": {"alwaysAvailable": "Toujours disponible", "unavailableByDefault": "Indisponible par défaut", "specificDates": "Disponible à des dates spécifiques"}, "availabilityCalendar": "Sélectionnez les dates disponibles"}, "step10": {"title": "Étape 10 : Vérification et soumission", "description": "Vérifiez les détails de votre annonce avant de la soumettre.", "reviewListing": "Vérifiez votre annonce", "termsAgreement": "En soumettant cette annonce, vous acceptez nos Conditions d'utilisation et notre Politique de confidentialité", "submitListing": "Soumettre l'annonce"}}, "AuthorPage": {"listingsTitle": "<PERSON><PERSON><PERSON> de {name}", "listingsDescription": "Les annonces de {name} sont très riches, les avis 5 étoiles l'aident à être plus reconnu.", "showMore": "Montrez-moi plus", "reviewsTitle": "Avis ({count} avis)", "viewMoreReviews": "Voir {count} avis supplémentaires", "speaks": "<PERSON><PERSON> {language}", "joinedIn": "Inscrit en {month} {year}"}, "UploadBlog": {"pageTitle": "Télécharger un nouveau blog", "uploadProgress": "Progression du téléchargement : {progress}%", "formLabels": {"title": "Titre", "content": "Contenu", "excerpt": "Extrait", "category": "<PERSON><PERSON><PERSON><PERSON>", "tags": "Tags", "featuredImage": "Image à la une"}, "placeholders": {"title": "Entrez le titre du blog", "content": "Écrivez le contenu de votre blog ici...", "excerpt": "Entrez un bref extrait", "category": "Sélectionnez une catégorie"}, "buttons": {"addFaq": "Ajouter une FAQ", "removeFaq": "<PERSON><PERSON><PERSON><PERSON>", "submit": "So<PERSON><PERSON><PERSON>", "submitting": "Soumission en cours..."}, "faq": {"title": "FAQs", "questionPlaceholder": "Question", "answerPlaceholder": "Réponse"}, "messages": {"imageProcessed": "Image traitée avec succès !", "imageProcessError": "Échec du traitement de l'image.", "uploadError": "Échec du téléchargement de l'image.", "blogPostSuccess": "Article de blog téléchargé avec succès !", "blogPostError": "Échec de la soumission de l'article de blog."}}, "BlogPostContent": {"author": {"anonymous": "Anonyme"}, "buttons": {"like": "<PERSON>'aime", "share": "Partager", "comment": "<PERSON><PERSON><PERSON>"}, "sections": {"faq": "Questions fréquemment posées", "comments": "Commentaires", "relatedPosts": "Articles connexes"}, "placeholders": {"commentInput": "Ajouter un commentaire..."}, "loading": "Chargement..."}, "RelatedPosts": {"title": "Articles connexes"}, "BlogCard": {"general": "Général", "anonymous": "Anonyme"}, "Pagination1": {"pageLabel": "Page {number}"}, "RecommendedSection": {"title": "Recommandé pour vous", "discover": "Découvrir"}, "CategorySortFilter": {"blogTitle": "Blog", "blogDescription": "<PERSON><PERSON>, nous partageons des conseils de voyage, des guides de destination et des histoires qui inspirent votre prochaine aventure.", "all": "<PERSON>ut", "sortBy": "Trier par :", "newest": "Plus récent", "oldest": "Plus ancien"}, "BlogFilter": {"all": "<PERSON>ut"}, "BlogContent": {"loading": "Chargement..."}, "HeroSection": {"title": "Explorez le monde avec nous", "subtitle": "Découvrez des conseils de voyage, des guides et des histoires qui inspirent votre prochaine aventure.", "cta": "Commencez votre voyage"}, "Sidebar": {"newsletter": {"title": "Abonnez-vous à la newsletter de réservation almindhar", "placeholder": "Entrez votre email", "subscribe": "<PERSON>'abonner"}, "helpCenter": {"title": "Centre d'aide", "guest": {"title": "Invi<PERSON>", "accountAccess": "Accédez et gérez votre compte", "reservationHelp": "Aide pour une réservation"}, "host": {"title": "<PERSON><PERSON><PERSON>", "preparingToHost": "Se préparer à héberger", "hostingHelp": "Aide à l'hébergement"}}, "cta": {"title": "Explorez davantage pour trouver votre zone de confort", "subtitle": "Réservez votre séjour parfait avec nous.", "button": "Réserver maintenant"}}, "CheckOutPage": {"title": "Confirmer et payer", "yourStay": "<PERSON><PERSON><PERSON>", "viewBookingDetails": "Voir les détails de la réservation", "date": "Date", "guests": "Invités", "adults": "Adultes", "children": "<PERSON><PERSON><PERSON>", "payWith": "Payer avec", "paypal": "<PERSON><PERSON>", "creditCard": "Carte de <PERSON>", "confirmAndPay": "Confirmer et payer", "priceDetails": "Détails du prix", "days": "jours", "serviceFee": "Frais de service", "total": "Total"}, "PageContact": {"title": "Contact", "address": "ADRESSE", "email": "EMAIL", "phone": "TÉLÉPHONE", "socialMedia": "RÉSEAUX SOCIAUX", "fullName": "Nom complet", "emailAddress": "Adresse e-mail", "message": "Message", "sendMessage": "Envoyer le message"}, "PageLogin": {"title": "Connexion", "continueWith": "Continuer avec", "or": "OU", "emailPlaceholder": "Adresse e-mail", "passwordPlaceholder": "Mot de passe", "continue": "<PERSON><PERSON><PERSON>", "newUser": "Nouveau utilisateur ? Créer un compte"}, "MessagesPage": {"title": "Messages avec l'utilisateur {userId}", "loginPrompt": "Veuillez vous connecter pour voir les messages"}, "PayDonePage": {"paymentSuccessful": "Paiement ré<PERSON> !", "processingPayment": "Traitement du paiement...", "bookingConfirmed": "Votre réservation a été confirmée. Vous recevrez un e-mail de confirmation sous peu.", "processingMessage": "Nous traitons votre paiement. Cela peut prendre quelques instants.", "bookingDetails": "Détails de la réservation", "bookingId": "ID de réservation", "checkIn": "Arrivée", "checkOut": "<PERSON><PERSON><PERSON><PERSON>", "totalPrice": "Prix total", "viewMyBookings": "Voir mes réservations", "returnHome": "Retour à l'accueil"}, "PrivatePage": {"greeting": "Bonjour {email}"}, "PageSignUp": {"title": "Inscription", "emailLabel": "Adresse e-mail", "emailPlaceholder": "<EMAIL>", "passwordLabel": "Mot de passe", "userTypeLabel": "Vous vous inscrivez en tant que :", "individual": "<PERSON><PERSON><PERSON><PERSON>", "business": "Entreprise", "continue": "<PERSON><PERSON><PERSON>", "alreadyHaveAccount": "Vous avez déjà un compte ? Connectez-vous"}, "PageSubscription": {"title": "Abonnement", "subtitle": "Tarification adaptée aux besoins de toute taille d'entreprise.", "popular": "POPULAIRE", "month": "/mois", "signUp": "S'inscrire", "plans": {"starter": {"name": "Débutant", "features": ["Rapports automatisés", "Traitement plus rapide", "Personnalisations"], "description": "<PERSON><PERSON><PERSON><PERSON>, vous n'en avez probablement jamais entendu parler."}, "basic": {"name": "Basique", "features": ["Tout dans Débutant", "100 constructions", "Rapports d'avancement", "Support premium"], "description": "<PERSON><PERSON><PERSON><PERSON>, vous n'en avez probablement jamais entendu parler."}, "plus": {"name": "Plus", "features": ["Tout dans Basique", "Constructions illimitées", "Analyses avancées", "Évaluations d'entreprise"], "description": "<PERSON><PERSON><PERSON><PERSON>, vous n'en avez probablement jamais entendu parler."}}}, "AdminDashboard": {"title": "Tableau de bord administrateur", "tabs": {"dashboard": "Tableau de bord", "properties": "Propriétés", "users": "Utilisateurs", "reports": "Rapports", "bookings": "Historique des réservations"}, "metrics": {"totalIncome": "Revenu total", "totalVisitors": "Visiteurs totaux", "totalBookings": "Réservations totales", "revenue": "<PERSON><PERSON><PERSON> d'affaires"}, "search": "Rechercher...", "notifications": "Notifications", "addNewProperty": "Ajouter une nouvelle propriété", "propertyStatus": {"all": "Tous les statuts", "active": "Actif", "disapproved": "<PERSON><PERSON><PERSON><PERSON>", "pending": "En attente", "draft": "Brouillon"}, "propertyTable": {"property": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "location": "Emplacement", "status": "Statut", "price": "Prix", "host": "<PERSON><PERSON><PERSON>", "created": "<PERSON><PERSON><PERSON>", "actions": "Actions"}, "propertyActions": {"edit": "Modifier la propriété", "viewDetails": "Voir les détails", "markFeatured": "<PERSON><PERSON> comme en vedette", "delete": "Supprimer la propriété", "approve": "Approuver l'annonce", "reject": "Rejeter l'annonce"}, "propertyForm": {"editTitle": "Modifier la propriété", "addTitle": "Ajouter une nouvelle propriété", "editDescription": "Met<PERSON>z à jour les informations de la propriété ci-dessous.", "addDescription": "Remplissez le formulaire pour ajouter une nouvelle propriété.", "name": "Nom", "location": "Emplacement", "status": "Statut", "price": "Prix", "cancel": "Annuler", "featured": "Mar<PERSON> comme en vedette ?", "save": "Enregistrer"}, "users": {"title": "Utilisateurs", "addUser": "Ajouter un utilisateur", "table": {"name": "Nom", "email": "Email", "listings": "Annonces", "status": "Statut", "actions": "Actions"}, "editUser": "Modifier l'utilisateur", "newPassword": "Nouveau mot de passe", "passwordPlaceholder": "Laissez vide si inchangé"}, "reports": {"title": "Rapports", "period": {"weekly": "Hebdomadaire", "monthly": "<PERSON><PERSON><PERSON>", "yearly": "<PERSON><PERSON>"}, "downloadReport": "Télécharger le rapport", "metrics": {"totalRevenue": "Revenu total", "bookings": "Réservations", "averageStay": "<PERSON><PERSON><PERSON> moyenne de <PERSON>", "occupancyRate": "Taux d'occupation"}, "charts": {"revenueOverview": "Aperçu des revenus", "bookingStatistics": "Statistiques de réservation"}}, "bookings": {"title": "Historique des réservations", "search": "<PERSON><PERSON><PERSON>", "viewAll": "Voir tout", "table": {"name": "Nom", "bookingRef": "Réf. de réservation", "listings": "Annonces", "checkIn": "Arrivée", "checkOut": "<PERSON><PERSON><PERSON><PERSON>", "status": "Statut", "amount": "<PERSON><PERSON>", "action": "Action"}}, "cards": {"addCard": "Ajouter une carte"}, "common": {"edit": "Modifier", "delete": "<PERSON><PERSON><PERSON><PERSON>"}}, "CarCard": {"seats": "places", "perDay": "/jour", "ads": "PUB"}, "CarCardH": {"seats": "places", "autoGearbox": "Boîte automatique", "bags": "bagages", "carOwner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "perDay": "/jour"}, "CardAuthorBox": {"location": "New York"}, "BtnLikeIcon": {"title": "Enregistrer"}, "CardCategory1": {"articles": "Articles"}, "CardCategory3": {"properties": "propriétés"}, "CardCategory4": {"properties": "propriétés", "cars": "voitures", "experiences": "expériences"}, "CardCategory5": {"properties": "propriétés"}, "CardCategory6": {"properties": "propriétés"}, "CategoryBadgeList": {"defaultColor": "bleu"}}