import { NextResponse } from 'next/server';
import { welcomeEmailTranslations, SupportedLanguage } from '@/utils/email';
import { emailService } from '@/utils/emailService';
import { renderWelcomeEmail } from '@/utils/emailRenderer';
import { createClient } from '@/utils/supabase/server';
import { shouldSendEmail, logSkippedEmail } from '@/utils/emailPreferences';

interface WelcomeEmailRequest {
  email: string;
  name?: string;
  language?: SupportedLanguage;
  userId?: string;
}

// Function to fetch sample listings for the welcome email
async function fetchSampleListings() {
  try {
    // Create and await the Supabase client
    const supabase = await createClient();

    // Fetch premium listings (higher price) with proper joins
    const { data: premiumListings, error: premiumError } = await supabase
      .from('listings')
      .select(`
        id,
        title,
        featured_image_url,
        num_guests,
        listing_pricing(id, nightly_rate),
        cities!city_id(name),
        states!state_id(name),
        listing_ratings_summary(average_rating, review_count)
      `)
      .limit(4);

    if (premiumError) {
      console.error('Error fetching premium listings:', premiumError);
    }

    // Fetch budget-friendly listings with proper joins
    const { data: budgetListings, error: budgetError } = await supabase
      .from('listings')
      .select(`
        id,
        title,
        featured_image_url,
        num_guests,
        listing_pricing(id, nightly_rate),
        cities!city_id(name),
        states!state_id(name),
        listing_ratings_summary(average_rating, review_count)
      `)
      .order('created_at', { ascending: false })
      .limit(4);

    if (budgetError) {
      console.error('Error fetching budget listings:', budgetError);
    }

    console.log('Raw Premium Listings:', JSON.stringify(premiumListings, null, 2));
    console.log('Raw Budget Listings:', JSON.stringify(budgetListings, null, 2));

    // Format the data to match what the email renderer expects
    const formatListings = (listings: any[] | null) => {
      if (!listings || listings.length === 0) return [];
      
      return listings.map(listing => ({
        id: listing.id,
        title: listing.title,
        featured_image_url: listing.featured_image_url,
        num_guests: listing.num_guests,
        nightly_rate: listing.listing_pricing?.[0]?.nightly_rate || 0,
        average_rating: listing.listing_ratings_summary?.[0]?.average_rating || 0,
        review_count: listing.listing_ratings_summary?.[0]?.review_count || 0,
        city_name: listing.cities?.name || 'Unknown',
        state_name: listing.states?.name || 'Unknown'
      }));
    };

    const formattedPremium = formatListings(premiumListings);
    const formattedBudget = formatListings(budgetListings);

    console.log('Formatted Premium Listings:', formattedPremium);
    console.log('Formatted Budget Listings:', formattedBudget);

    return {
      premiumListings: formattedPremium,
      budgetListings: formattedBudget
    };
  } catch (error) {
    console.error('Error in fetchSampleListings:', error);
    return {
      premiumListings: [],
      budgetListings: []
    };
  }
}

export async function POST(req: Request) {
  try {
    const { email, name, language = 'fr', userId } = await req.json() as WelcomeEmailRequest;

    if (!email) {
      console.error('Welcome email request missing email');
      return NextResponse.json(
        { error: 'Email is required' },
        { status: 400 }
      );
    }

    // If userId is provided, check user preferences
    if (userId) {
      const shouldSend = await shouldSendEmail(userId, 'newsletter_welcome');
      
      if (!shouldSend) {
        console.log(`User ${userId} has opted out of newsletter emails`);
        
        // Create supabase client for logging
        const supabase = await createClient();
        
        // Log skipped email
        await logSkippedEmail(supabase, {
          user_id: userId,
          email: email,
          email_type: 'newsletter_welcome'
        });
        
        return NextResponse.json(
          { 
            message: 'Welcome email skipped due to user preferences',
            skipped: true
          },
          { status: 200 }
        );
      }
    }

    // Fetch sample listings to include in the welcome email
    const { premiumListings, budgetListings } = await fetchSampleListings();

    // Generate rich HTML email using the renderer
    const emailHtml = renderWelcomeEmail({
      name,
      language,
      premiumListings,
      budgetListings
    });

    const t = welcomeEmailTranslations[language];
    
    // Send the email
    const result = await emailService.sendEmail({
      to: email,
      subject: t.subject,
      html: emailHtml,
    });

    if (!result.success) {
      console.error("Error sending welcome email:", {
        error: result.error,
        email,
        name,
        language
      });
      return NextResponse.json(
        { error: result.error || 'Failed to send welcome email' },
        { status: 500 }
      );
    }

    return NextResponse.json(
      {
        message: 'Welcome email sent successfully',
        messageId: result.messageId
      },
      { status: 200 }
    );

  } catch (error: any) {
    console.error("Unexpected error in welcome email API:", {
      error: error.message,
      stack: error.stack
    });
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
