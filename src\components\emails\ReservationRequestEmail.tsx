import { ReactElement } from 'react';

interface ReservationRequestEmailProps {
    hostName: string;
    propertyTitle: string;
    propertyDescription: string;
    propertyImage: string;
    checkInDate: string;
    checkInTime: string;
    checkOutDate: string;
    checkOutTime: string;
    pricePerNight: number;
    currency: string;
    guestName: string;
    guestEmail: string;
    guestAvatarUrl?: string;
    dashboardUrl: string;
    language?: 'en' | 'fr' | 'ar';
}

export const translations = {
    en: {
        subject: 'New Reservation Request',
        preview: 'You have received a new reservation request for your property',
        notificationText: 'You have received a new reservation request for your property. Please review the details below and manage the reservation from your dashboard.',
        greeting: 'Hello',
        manageButton: 'Manage reservation',
        entireProperty: 'Entire property hosted by {host_name}',
        checkIn: 'Check-in',
        checkOut: 'Check-out',
        entryFrom: 'Entry from',
        pricePerNight: 'Price per night',
        meetYourGuest: 'Meet your guest'
    },
    fr: {
        subject: 'Nouvelle demande de réservation',
        preview: 'Vous avez reçu une nouvelle demande de réservation pour votre logement',
        notificationText: 'Vous avez reçu une nouvelle demande de réservation pour votre logement. Veuillez examiner les détails ci-dessous et gérer la réservation depuis votre tableau de bord.',
        greeting: 'Bonjour',
        manageButton: 'Gérer la réservation',
        entireProperty: 'logement entier hébergé par {host_name}',
        checkIn: 'Arrivée',
        checkOut: 'Départ',
        entryFrom: 'L\'entrée à partir de',
        pricePerNight: 'Prix de la nuité',
        meetYourGuest: 'Rencontrez votre locataire'
    },
    ar: {
        subject: 'طلب حجز جديد',
        preview: 'لقد تلقيت طلب حجز جديد لعقارك',
        notificationText: 'لقد تلقيت طلب حجز جديد لعقارك. يرجى مراجعة التفاصيل أدناه وإدارة الحجز من لوحة التحكم الخاصة بك.',
        greeting: 'مرحباً',
        manageButton: 'إدارة الحجز',
        entireProperty: 'العقار بالكامل يستضيفه {host_name}',
        checkIn: 'تسجيل الدخول',
        checkOut: 'تسجيل الخروج',
        entryFrom: 'الدخول من',
        pricePerNight: 'السعر لكل ليلة',
        meetYourGuest: 'تعرف على ضيفك'
    }
};

export const ReservationRequestEmail = ({
    hostName,
    propertyTitle,
    propertyImage,
    checkInDate,
    checkInTime,
    checkOutDate,
    checkOutTime,
    pricePerNight,
    currency,
    guestName,
    guestEmail,
    guestAvatarUrl,
    dashboardUrl,
    language = 'fr'
}: ReservationRequestEmailProps): ReactElement => {
    const t = translations[language];
    const isRTL = language === 'ar';
    
    // Format currency correctly (e.g., "9 DT" instead of "dt 9")
    const formattedPrice = `${pricePerNight.toString()} ${currency.toUpperCase()}`;

    return (
        <div style={{ 
            fontFamily: 'Arial, sans-serif',
            width: '100%', 
            margin: '0 auto',
            backgroundColor: '#ffffff',
            padding: '30px 20px',
            boxSizing: 'border-box'
        }}>
            {/* Responsive styles */}
            <style dangerouslySetInnerHTML={{ __html: `
                @media only screen and (max-width: 480px) {
                    .mobile-text {
                        font-size: 14px !important;
                    }
                    .mobile-heading {
                        font-size: 20px !important;
                    }
                    .mobile-subheading {
                        font-size: 16px !important;
                    }
                    .mobile-small {
                        font-size: 12px !important;
                    }
                    .mobile-button {
                        padding: 12px 16px !important;
                        font-size: 14px !important;
                    }
                    .mobile-container {
                        padding: 12px !important;
                    }
                    .mobile-guest-email {
                        word-break: break-all !important;
                        font-size: 13px !important;
                    }
                    .mobile-avatar {
                        width: 50px !important;
                        height: 50px !important;
                    }
                    .centered-container {
                        padding: 0 !important;
                    }
                }
            `}} />
            
            {/* Logo Only Header */}
            <div style={{ marginBottom: '30px' }}>
                <img 
                    src="https://api.almindharbooking.com/storage/v1/object/public/email-assets//Welcome_email_logo.png"
                    alt="Almindhar Booking"
                    style={{ height: '40px' }}
                />
            </div>

            {/* Greeting */}
            <p className="mobile-text" style={{
                color: '#333',
                fontSize: '16px',
                marginBottom: '20px',
                textAlign: isRTL ? 'right' : 'left'
            }}>
                {t.greeting} {hostName},
            </p>

            {/* Notification Text */}
            <p className="mobile-text" style={{
                color: '#666',
                fontSize: '16px',
                lineHeight: 1.5,
                marginBottom: '30px',
                textAlign: isRTL ? 'right' : 'left'
            }}>
                {t.notificationText}
            </p>

            {/* Centered container for buttons and property image */}
            <div className="centered-container" style={{
                maxWidth: '600px',
                margin: '0 auto',
                padding: '0 10px'
            }}>
                {/* Action Button - Single button for dashboard */}
                <table cellPadding="0" cellSpacing="0" style={{ width: '100%', marginBottom: '30px' }}>
                    <tr>
                        <td style={{ padding: '0' }}>
                            <a 
                                href={dashboardUrl}
                                className="mobile-button"
                                style={{
                                    display: 'inline-block',
                                    backgroundColor: '#0F172A',
                                    color: 'white',
                                    padding: '14px 24px',
                                    textDecoration: 'none',
                                    borderRadius: '10px',
                                    textAlign: 'center',
                                    width: '100%',
                                    fontWeight: 600,
                                    fontSize: '16px',
                                    boxSizing: 'border-box'
                                }}
                            >
                                {t.manageButton}
                            </a>
                        </td>
                    </tr>
                </table>

                {/* Property Image - Responsive width */}
                <div style={{ marginBottom: '20px' }}>
                    <img 
                        src={propertyImage}
                        alt={propertyTitle}
                        style={{
                            width: '100%',
                            maxWidth: '100%',
                            height: 'auto',
                            minHeight: '200px',
                            objectFit: 'cover',
                            borderRadius: '12px'
                        }}
                    />
                </div>
            </div>

            {/* Property Title Only */}
            <h2 className="mobile-heading" style={{
                fontSize: '24px',
                fontWeight: '600',
                color: '#0F172A',
                margin: '0 0 8px 0',
                textAlign: isRTL ? 'right' : 'left'
            }}>
                {propertyTitle}
            </h2>

            {/* Host Info - Keep this line */}
            <p className="mobile-text" style={{
                color: '#64748B',
                fontSize: '16px',
                lineHeight: '1.5',
                margin: '0 0 20px 0',
                textAlign: isRTL ? 'right' : 'left'
            }}>
                {t.entireProperty.replace('{host_name}', hostName)}
            </p>

            {/* Reservation Details */}
            <table cellPadding="0" cellSpacing="0" className="mobile-container" style={{ width: '100%', marginBottom: '30px', backgroundColor: '#F8FAFC', borderRadius: '12px', padding: '16px' }}>
                <tr>
                    <td style={{ width: '50%', padding: `0 ${isRTL ? '0 0 10px' : '10px 0 0'}`, verticalAlign: 'top' }}>
                        <p className="mobile-small" style={{ color: '#64748B', margin: '0 0 4px 0', fontSize: '14px' }}>{t.checkIn}</p>
                        <p className="mobile-text" style={{ color: '#0F172A', margin: '0 0 4px 0', fontWeight: 600, fontSize: '16px' }}>{checkInDate}</p>
                        <p className="mobile-small" style={{ color: '#64748B', margin: '0', fontSize: '14px' }}>{t.entryFrom} {checkInTime}</p>
                    </td>
                    <td style={{ width: '50%', padding: `0 ${isRTL ? '10px 0 0' : '0 0 10px'}`, verticalAlign: 'top' }}>
                        <p className="mobile-small" style={{ color: '#64748B', margin: '0 0 4px 0', fontSize: '14px' }}>{t.checkOut}</p>
                        <p className="mobile-text" style={{ color: '#0F172A', margin: '0 0 4px 0', fontWeight: 600, fontSize: '16px' }}>{checkOutDate}</p>
                        <p className="mobile-small" style={{ color: '#64748B', margin: '0', fontSize: '14px' }}>{checkOutTime}</p>
                    </td>
                </tr>
            </table>

            {/* Price - Updated format */}
            <div className="mobile-container" style={{ marginBottom: '30px', backgroundColor: '#F8FAFC', borderRadius: '12px', padding: '16px' }}>
                <p className="mobile-small" style={{ color: '#64748B', margin: '0 0 4px 0', fontSize: '14px' }}>{t.pricePerNight}</p>
                <p className="mobile-text" style={{ color: '#0F172A', margin: '0', fontWeight: 600, fontSize: '18px' }}>
                    {formattedPrice}
                </p>
            </div>

            {/* Guest Details - Improved styling */}
            <div className="mobile-container" style={{ 
                marginBottom: '20px',
                backgroundColor: '#F8FAFC',
                borderRadius: '12px',
                padding: '20px'
            }}>
                <h3 className="mobile-subheading" style={{
                    color: '#0F172A',
                    margin: '0 0 16px 0',
                    fontSize: '18px',
                    fontWeight: '600',
                    textAlign: isRTL ? 'right' : 'left'
                }}>
                    {t.meetYourGuest}
                </h3>

                <div style={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: isRTL ? 'flex-end' : 'flex-start',
                    gap: '16px'
                }}>
                    {/* Use actual avatar if available, otherwise use initial */}
                    {guestAvatarUrl ? (
                        <img 
                            src={guestAvatarUrl}
                            alt={guestName}
                            className="mobile-avatar"
                            style={{
                                width: '60px',
                                height: '60px',
                                borderRadius: '50%',
                                objectFit: 'cover'
                            }}
                        />
                    ) : (
                        <div className="mobile-avatar" style={{
                            width: '60px',
                            height: '60px',
                            borderRadius: '50%',
                            backgroundColor: '#F96F5D',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center'
                        }}>
                            <span style={{ color: 'white', fontWeight: 600, fontSize: '24px' }}>
                                {guestName.charAt(0).toUpperCase()}
                            </span>
                        </div>
                    )}
                    
                    <div style={{ maxWidth: 'calc(100% - 80px)' }}>
                        <p className="mobile-text" style={{
                            color: '#0F172A',
                            margin: '0 0 4px 0',
                            fontWeight: 600,
                            fontSize: '18px',
                            textAlign: isRTL ? 'right' : 'left'
                        }}>
                            {guestName}
                        </p>
                        <p className="mobile-guest-email" style={{
                            color: '#64748B',
                            margin: '0',
                            fontSize: '16px',
                            textAlign: isRTL ? 'right' : 'left',
                            wordBreak: 'break-word',
                            overflowWrap: 'break-word'
                        }}>
                            {guestEmail}
                        </p>
                    </div>
                </div>
            </div>
            
            {/* Footer */}
            <div className="mobile-small" style={{ 
                textAlign: 'center',
                marginTop: '40px',
                color: '#64748B',
                fontSize: '14px'
            }}>
                &copy; 2024 Almindhar Booking. {language === 'fr' ? 'Tous droits réservés.' : language === 'ar' ? 'جميع الحقوق محفوظة.' : 'All rights reserved.'}
            </div>
        </div>
    );
};