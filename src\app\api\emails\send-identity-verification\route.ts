import { createClient } from '@/utils/supabase/server';
import { NextResponse } from 'next/server';
import { supabaseAdmin } from '@/lib/supabase';
import { emailService } from '@/utils/emailService';
import { generateIdentityVerificationEmail } from '@/components/emails/IdentityVerificationEmail';
import { shouldSendEmail, logSkippedEmail } from '@/utils/emailPreferences';

interface WebhookPayload {
    type: 'UPDATE';
    table: 'identity_verifications';
    record: {
        id: string;
        user_id: string;
        status: string;
        rejection_reason?: string;
    };
    old_record: {
        status: string;
    };
}

export async function POST(request: Request) {
    try {
        const payload = await request.json() as WebhookPayload;
        
        // Validate the payload
        if (
            payload.type !== 'UPDATE' ||
            payload.table !== 'identity_verifications' ||
            !payload.record.id ||
            !payload.record.user_id ||
            payload.record.status === payload.old_record.status
        ) {
            console.error('Invalid webhook payload:', payload);
            return NextResponse.json({ error: 'Invalid webhook payload' }, { status: 400 });
        }

        // Check if this is a status change we care about (approved or declined)
        const newStatus = payload.record.status;
        const oldStatus = payload.old_record.status;
        
        if (
            !(newStatus === 'approuved' && oldStatus !== 'approuved') && 
            !(newStatus === 'declined' && oldStatus !== 'declined')
        ) {
            console.log('Status change does not require notification:', { oldStatus, newStatus });
            return NextResponse.json({ 
                message: 'Status change does not require notification',
                skipped: true
            });
        }

        const supabase = await createClient();
        
        // Fetch user profile to get email and name
        const { data: profile, error: profileError } = await supabase
            .from('profiles')
            .select('email, first_name, last_name')
            .eq('id', payload.record.user_id)
            .single();

        if (profileError || !profile) {
            console.error('Error fetching user profile:', profileError);
            return NextResponse.json({ error: 'Failed to fetch user details' }, { status: 500 });
        }

        const userEmail = profile.email;
        const userName = profile.first_name || 'User';
        
        if (!userEmail) {
            console.error('No email found for user');
            return NextResponse.json({ error: 'User email not found' }, { status: 404 });
        }

        // Check if user has opted in to receive system notifications
        const shouldSend = await shouldSendEmail(
            payload.record.user_id,
            'identity_verification'
        );
        
        if (!shouldSend) {
            console.log(`User ${payload.record.user_id} has opted out of system notifications`);
            
            // Log skipped email
            await logSkippedEmail(supabase, {
                user_id: payload.record.user_id,
                email: userEmail,
                email_type: 'identity_verification',
                reference_id: payload.record.id
            });
            
            return NextResponse.json({ 
                message: 'Email skipped due to user preferences',
                skipped: true
            });
        }

        // Determine email type based on status
        const emailStatus = newStatus === 'approuved' ? 'approved' : 'rejected';
        const rejectionReason = payload.record.rejection_reason;
        
        // Generate and send email
        try {
            const emailHtml = generateIdentityVerificationEmail({
                userName,
                status: emailStatus,
                rejectionReason: emailStatus === 'rejected' ? rejectionReason : undefined,
                language: 'fr' // Default to French
            });

            const subject = emailStatus === 'approved' 
                ? 'Votre vérification d\'identité a été approuvée' 
                : 'Votre vérification d\'identité nécessite votre attention';

            await emailService.sendEmail({
                to: userEmail,
                subject,
                html: emailHtml
            });

            // Log successful email
            await supabase
                .from('email_logs')
                .insert({
                    email_type: 'identity_verification',
                    user_id: payload.record.user_id,
                    email: userEmail,
                    reference_id: payload.record.id,
                    status: 'success',
                    sent_at: new Date().toISOString()
                });

            return NextResponse.json({
                success: true,
                message: `Identity verification ${emailStatus} email sent successfully`
            });

        } catch (emailError) {
            // Log failed email attempt
            await supabase
                .from('email_logs')
                .insert({
                    email_type: 'identity_verification',
                    user_id: payload.record.user_id,
                    email: userEmail,
                    reference_id: payload.record.id,
                    status: 'failed',
                    error: emailError instanceof Error ? emailError.message : 'Unknown error',
                    sent_at: null
                });

            throw emailError;
        }

    } catch (error) {
        console.error('Error in identity verification email:', error);
        return NextResponse.json(
            {
                error: 'Failed to send identity verification email',
                details: error instanceof Error ? error.message : 'Unknown error'
            },
            { status: 500 }
        );
    }
}
