import { createClient } from "@/utils/supabase/server";
import { NextResponse } from 'next/server';

export async function GET(request: Request) {
  try {
    const supabase = await createClient();
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      return NextResponse.json({ error: "User not authenticated" }, { status: 401 });
    }
    
    console.log("Checking verification status for user:", user.id);
    
    // Get the raw data from the database to see all available fields
    const { data: rawData, error: rawError } = await supabase
      .from("identity_verifications")
      .select("*")
      .eq("user_id", user.id)
      .order("created_at", { ascending: false })
      .limit(1)
      .single();
      
    if (rawError) {
      if (rawError.code === "PGRST116") {
        return NextResponse.json({ status: "not_submitted" }, { status: 200 });
      }
      throw rawError;
    }
    
    // Log all columns to identify the correct rejection reason field
    console.log("All database columns:", Object.keys(rawData));
    console.log("Complete raw data:", JSON.stringify(rawData, null, 2));
    console.log("Raw DB status:", rawData.status);
    
    let uiStatus: "pending" | "verified" | "rejected" | "not_submitted" = "not_submitted";
    let rejectionReason: string | null = null;
    
    if (rawData.status) {
      const dbStatus = rawData.status.toLowerCase();
      console.log("Raw DB status:", dbStatus);
      
      if (dbStatus === "pending") {
        uiStatus = "pending";
      } else if (dbStatus === "approuved" || dbStatus === "approved") {
        uiStatus = "verified";
      } else if (dbStatus === "declined" || dbStatus === "rejected") {
        uiStatus = "rejected";
        
        // IMPORTANT: Check all possible field names for the rejection reason
        console.log("Looking for rejection reason in all fields:");
        Object.entries(rawData).forEach(([key, value]) => {
          console.log(`Field - ${key}:`, value);
        });
        
        // Try all possible field names for the rejection reason
        if (typeof rawData.rejection_reason === 'string' && rawData.rejection_reason) {
          rejectionReason = rawData.rejection_reason;
          console.log("Found rejection_reason:", rejectionReason);
        } else if (typeof rawData.rejectionReason === 'string' && rawData.rejectionReason) {
          rejectionReason = rawData.rejectionReason;
          console.log("Found rejectionReason:", rejectionReason);
        } else if (typeof rawData.declined_reason === 'string' && rawData.declined_reason) {
          rejectionReason = rawData.declined_reason;
          console.log("Found declined_reason:", rejectionReason);
        } else {
          // Default rejection reason if none is found
          rejectionReason = "Vos documents n'ont pas pu être vérifiés.";
          console.log("No rejection reason found, using default:", rejectionReason);
        }
      }
    }
    
    // CRITICAL FIX: Ensure the rejection reason is properly included in the response
    // and is always a string, never undefined
    const response = { 
      status: uiStatus,
      rejectionReason: rejectionReason || "Vos documents n'ont pas pu être vérifiés."
    };
    
    console.log("Sending verification response:", JSON.stringify(response, null, 2));
    
    return NextResponse.json(response, { status: 200 });
  } catch (error: any) {
    console.error("Verification check error:", error);
    return NextResponse.json({ error: error.message || "Error checking verification status" }, { status: 500 });
  }
}
