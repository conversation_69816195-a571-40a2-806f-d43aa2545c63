import { createClient } from '@/utils/supabase/server';
import { NextResponse } from 'next/server';
import { initializeEmailService } from '@/utils/emailProviders';
import { ReservationRequestEmail, translations } from '@/components/emails/ReservationRequestEmail';
import { shouldSendEmail, logSkippedEmail } from '@/utils/emailPreferences';
import { randomUUID } from 'crypto';

// Define supported languages type
type SupportedLanguage = 'en' | 'fr' | 'ar';

// Define profile type with proper language typing
type Profile = {
    email: string;
    fullname: string;
    language: SupportedLanguage | null;
}

interface WebhookPayload {
    type: 'INSERT';
    table: 'bookings';
    record: {
        id: string;
        listing_id: string;
        user_id: string;
        start_date: string;
        end_date: string;
        num_guests: number;
        status: 'pending';
        total_price: number;
        host_id: string;
    }
}

interface ListingPricing {
    arrival_time: string;
    departure_time: string;
}

interface ListingWithProfile {
    title: string;
    description: string;
    featured_image_url: string;
    host: Profile | null;
    pricing: ListingPricing;
}

export async function POST(request: Request) {
    try {
        console.log('Received webhook request');
        const payload = await request.json() as WebhookPayload;
        console.log('Payload:', payload);

        const supabase = await createClient();
        console.log('Supabase client created');

        // 1. Fetch listing and pricing information
        console.log('Fetching listing details for ID:', payload.record.listing_id);
        const { data: listingData } = await supabase
            .from('listings')
            .select(`
                title,
                description,
                featured_image_url,
                host_id,
                pricing:listing_pricing!listing_pricing_listing_id_fkey(
                    arrival_time,
                    departure_time
                )
            `)
            .eq('id', payload.record.listing_id)
            .single();

        console.log('Raw listing data:', listingData);

        if (!listingData) {
            return NextResponse.json({ error: 'Listing not found' }, { status: 404 });
        }

        // Extract pricing data with default values
        const pricing: ListingPricing = {
            arrival_time: listingData.pricing?.[0]?.arrival_time || '15:00',
            departure_time: listingData.pricing?.[0]?.departure_time || '12:00'
        };

        // 2. Fetch host profile - using id directly since profiles.id = auth.users.id
        console.log('Fetching host details for ID:', listingData.host_id);
        const { data: hostProfile } = await supabase
            .from('profiles')
            .select(`
                email,
                fullname,
                language
            `)
            .eq('id', listingData.host_id)  
            .single();

        console.log('Host profile:', hostProfile);

        // Create listing object with either found profile or default values
        const listing: ListingWithProfile = {
            title: listingData.title,
            description: listingData.description,
            featured_image_url: listingData.featured_image_url,
            host: hostProfile || null,
            pricing: pricing
        };

        // 3. Fetch guest details
        console.log('Fetching guest details for ID:', payload.record.user_id);
        const { data: guest } = await supabase
            .from('profiles')
            .select('email, fullname, avatar_url')
            .eq('id', payload.record.user_id)
            .single();

        console.log('Guest data:', guest);

        if (!guest) {
            return NextResponse.json({ error: 'Guest not found' }, { status: 404 });
        }

        // Generate dashboard URL for managing the booking
        const dashboardUrl = `${process.env.NEXT_PUBLIC_APP_URL}/dashboard/guests/${payload.record.id}`;
        console.log('Generated dashboard URL:', dashboardUrl);

        // 4. Check if host has opted in to receive booking updates
        const shouldSend = await shouldSendEmail(
            listingData.host_id,
            'reservation_request'
        );
        
        if (!shouldSend) {
            console.log(`Host ${listingData.host_id} has opted out of booking update emails`);
            
            // Log skipped email
            await logSkippedEmail(supabase, {
                user_id: listingData.host_id,
                email: listing.host?.email || '',
                email_type: 'reservation_request',
                reference_id: payload.record.id
            });
            
            // Still return success since we've handled the request properly
            return NextResponse.json({ 
                success: true,
                message: 'Email skipped due to host preferences'
            });
        }

        // 5. Send email with proper language handling
        const emailService = initializeEmailService();
        console.log('Email service initialized');

        const hostLanguage = (listing.host?.language || 'fr') as SupportedLanguage;
        console.log('Host language:', hostLanguage);

        // Use pricing data directly since it's guaranteed to have values
        const checkInTime = listing.pricing.arrival_time;
        const checkOutTime = listing.pricing.departure_time;
        console.log('Check-in/out times:', { checkInTime, checkOutTime });

        // Calculate price per night
        const pricePerNight = payload.record.total_price / getDaysBetween(
            payload.record.start_date,
            payload.record.end_date
        );

        console.log('Price per night:', pricePerNight);

        await emailService.sendEmail({
            to: listing.host?.email || '',
            subject: translations[hostLanguage].subject.replace(
                '{property_name}',
                listing.title
            ),
            react: ReservationRequestEmail({
                hostName: listing.host?.fullname || '',
                propertyTitle: listing.title,
                propertyDescription: listing.description,
                propertyImage: listing.featured_image_url,
                checkInDate: payload.record.start_date,
                checkOutDate: payload.record.end_date,
                checkInTime,
                checkOutTime,
                pricePerNight: Math.round(pricePerNight),
                currency: 'DT',
                guestName: guest.fullname,
                guestEmail: guest.email,
                guestAvatarUrl: guest.avatar_url,
                dashboardUrl,
                language: hostLanguage
            })
        });
        console.log('Email sent successfully');

        return NextResponse.json({ success: true });
    } catch (error: any) {
        console.error('Detailed error:', error);
        console.error('Error stack:', error.stack);
        return NextResponse.json({
            error: 'Failed to send email',
            details: error.message,
            stack: error.stack
        }, { status: 500 });
    }
}

function getDaysBetween(startDate: string, endDate: string): number {
    const start = new Date(startDate);
    const end = new Date(endDate);
    return Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24));
}