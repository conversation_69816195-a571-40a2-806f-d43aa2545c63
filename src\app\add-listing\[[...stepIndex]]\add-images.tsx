"use client"

import { useEffect, useState, useRef } from "react"
import Image from "next/image"
import { TrashIcon } from "@heroicons/react/24/outline"
import { X } from "lucide-react"
import CommonLayout from "./CommonLayout"
import { motion, AnimatePresence, LayoutGroup } from "framer-motion"
import * as DropdownMenu from '@radix-ui/react-dropdown-menu'
import { useFormContext } from "../FormContext"

// Main component for the Add Images step in the listing flow
export default function AddImages() {
    const { formErrors, coverFile, additionalFiles, setCoverFile, setAdditionalFiles, validateStep } = useFormContext()
    // Local errors for UI-level validation
    const [coverUploadError, setCoverUploadError] = useState<string>("")
    const [additionalUploadError, setAdditionalUploadError] = useState<string>("")
    // Extract first file-specific error key
    const fileErrorKey = Object.keys(formErrors).find(key => /^file\d+$/.test(key))
    const fileError = fileErrorKey ? formErrors[fileErrorKey] : ''
    // --- Modal State Management ---
    // Controls modal visibility for uploading images
    const [isUploadModalOpen, setIsUploadModalOpen] = useState(false);
    // Stores files selected in the modal (cover or additional images)
    const [modalSelectedFiles, setModalSelectedFiles] = useState<File[]>([]);
    // Preview URL for the currently selected cover image
    const [modalPreviewUrl, setModalPreviewUrl] = useState<string | null>(null);
    // Modal step: 'cover' for main image, 'additional' for more images
    const [modalStep, setModalStep] = useState<'cover' | 'additional'>('cover');
    // Animation direction for modal transitions
    const [modalDirection, setModalDirection] = useState<'forward' | 'back'>('forward');
    // Ref to reset modal scroll position between steps
    const modalContentRef = useRef<HTMLDivElement>(null);
    const [pendingModalClose, setPendingModalClose] = useState(false);

    // --- Persistent State for Uploaded Images ---
    // Stores the finalized cover image and its preview URL
    const [coverImage, setCoverImage] = useState<{ file: File; url: string } | null>(null);
    // Stores the finalized additional images and their preview URLs
    const [additionalImages, setAdditionalImages] = useState<{ file: File; url: string }[]>([]);

    // --- File input refs for direct upload ---
    const coverFileInputRef = useRef<HTMLInputElement>(null);
    const addMoreFileInputRef = useRef<HTMLInputElement>(null);

    // Add a ref to store stable keys for each File
    const additionalFileKeyMap = useRef(new Map<File, string>());

    // Helper to get or assign a stable key for a File
    function getFileKey(file: File) {
        let key = additionalFileKeyMap.current.get(file);
        if (!key) {
            key = crypto.randomUUID();
            additionalFileKeyMap.current.set(file, key);
        }
        return key;
    }
    // Clean up keys for removed files
    useEffect(() => {
        const currentFiles = new Set(additionalFiles);
        for (const file of Array.from(additionalFileKeyMap.current.keys())) {
            if (!currentFiles.has(file)) {
                additionalFileKeyMap.current.delete(file);
            }
        }
    }, [additionalFiles]);

    // Debug: Log component mount/unmount
    useEffect(() => {
        // Remove all console.log statements throughout the file
    }, []);

    // --- Modal Handlers ---
    // Handles file selection for the cover image step
    const handleModalFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const files = Array.from(e.target.files || [])
        setCoverUploadError("")
        if (!files.length) {
            setModalSelectedFiles([])
            setModalPreviewUrl(null)
            return
        }
        const file = files[0]
        // Validate type
        if (!file.type.match(/^image\/(jpeg|png)$/)) {
            setCoverUploadError('Format non supporté (jpeg/png uniquement).')
            return
        }
        // Validate size (≤5MB)
        if (file.size > 5 * 1024 * 1024) {
            setCoverUploadError('Chaque image doit faire ≤ 5 Mo.')
            return
        }
        // Accept file
        setModalSelectedFiles([file])
        setModalPreviewUrl(URL.createObjectURL(file))
    };
    // Handles advancing modal steps or closing modal after upload
    const handleModalSend = () => {
        if (modalStep === 'cover' && modalPreviewUrl) {
            setCoverFile(modalSelectedFiles[0]);
            setModalSelectedFiles([]);
            setModalDirection('forward');
            setTimeout(() => {
                setModalStep('additional');
            }, 0);
        } else if (modalStep === 'additional') {
            setAdditionalFiles(modalSelectedFiles);
            setPendingModalClose(true);
        }
    };

    // Handles going back to the cover photo step with reverse animation
    const handleModalBack = () => {
        setModalDirection('back');
        setTimeout(() => setModalStep('cover'), 0);
    };

    // Handles closing the modal and resetting modal state
    const handleModalClose = () => {
        setIsUploadModalOpen(false);
        setModalSelectedFiles([]);
        setModalPreviewUrl(null);
        setModalStep('cover');
    };
    // Handles file selection for the additional images step
    const handleAdditionalFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const files = Array.from(e.target.files || [])
        setAdditionalUploadError("")
        if (!files.length) return
        let newFiles = [...modalSelectedFiles]
        let invalidMsg = ''
        for (const file of files) {
            if (!file.type.match(/^image\/(jpeg|png)$/)) {
                invalidMsg = 'Format non supporté (jpeg/png uniquement).'
                continue
            }
            if (file.size > 5 * 1024 * 1024) {
                invalidMsg = 'Chaque image doit faire ≤ 5 Mo.'
                continue
            }
            newFiles.push(file)
        }
        setModalSelectedFiles(newFiles)
        // File-type or size errors take precedence
        if (invalidMsg) {
            setAdditionalUploadError(invalidMsg)
        } else if (newFiles.length > 0 && newFiles.length < 5) {
            setAdditionalUploadError('Veuillez télécharger au moins 5 photos supplémentaires.')
        } else {
            setAdditionalUploadError("")
        }
    };

    // --- Effects for Cleanup and UX ---
    // Clean up cover image preview URL only when it changes or modal closes
    useEffect(() => {
        return () => {
            if (modalPreviewUrl) URL.revokeObjectURL(modalPreviewUrl);
        };
    }, [modalPreviewUrl, isUploadModalOpen]);
    // Reset modal scroll position when changing steps
    useEffect(() => {
        if (modalContentRef.current) {
            modalContentRef.current.scrollTop = 0;
        }
    }, [modalStep]);

    // --- Handlers for direct cover image replacement ---
    const handleCoverFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        // Reset any previous cover error
        setCoverUploadError("")
        const files = Array.from(e.target.files || [])
        if (!files.length) return
        const file = files[0]
        // Validate type
        if (!file.type.match(/^image\/(jpeg|png)$/)) {
            setCoverUploadError('Format non supporté (jpeg/png uniquement).')
            return
        }
        // Validate size (≤5MB)
        if (file.size > 5 * 1024 * 1024) {
            setCoverUploadError('Chaque image doit faire ≤ 5 Mo.')
            return
        }
        // Persist valid cover to context
        setCoverFile(file)
        // Reset input value so the same file can be selected again if needed
        if (coverFileInputRef.current) coverFileInputRef.current.value = ""
    }

    // --- Handlers for direct additional image addition ---
    const handleAddMoreFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const files = Array.from(e.target.files || [])
        // Reset previous additional upload error
        setAdditionalUploadError("")
        if (!files.length) return
        // Validate and collect valid files
        const validFiles: File[] = []
        let invalidMsg = ""
        for (const file of files) {
            if (!file.type.match(/^image\/(jpeg|png)$/)) {
                invalidMsg = 'Format non supporté (jpeg/png uniquement).'
                continue
            }
            if (file.size > 5 * 1024 * 1024) {
                invalidMsg = 'Chaque image doit faire ≤ 5 Mo.'
                continue
            }
            validFiles.push(file)
        }
        // Append valid files to context
        if (validFiles.length) {
            setAdditionalFiles(prev => [...prev, ...validFiles])
            validateStep('add-images')
        }
        // Determine which local error to show
        if (invalidMsg) {
            setAdditionalUploadError(invalidMsg)
        } else if ((additionalFiles.length + validFiles.length) > 0 && (additionalFiles.length + validFiles.length) < 5) {
            setAdditionalUploadError('Veuillez télécharger au moins 5 photos supplémentaires.')
        } else {
            setAdditionalUploadError("")
        }
        // Reset input field
        if (addMoreFileInputRef.current) addMoreFileInputRef.current.value = ""
    }

    // --- UI Components ---
    // ImageCard: displays an image with optional badge and menu
    function ImageCard({ url, alt, badge, menu, isCover, index, onMenuAction }: { url: string; alt: string; badge?: string; menu?: boolean; isCover?: boolean; index?: number; onMenuAction?: (action: string, idx?: number) => void }) {
        return (
            <div className={`relative bg-neutral-200 rounded-lg w-full ${isCover ? 'h-72' : 'h-48'} overflow-hidden flex items-center justify-center`}>
                <Image src={url} alt={alt} fill className="object-cover w-full h-full rounded-lg" />
                {badge && (
                    <span className="absolute top-2 left-2 bg-white text-xs font-semibold rounded-[8px] py-[7px] px-[15px] shadow">
                        {badge}
                    </span>
                )}
                {menu && (
                    <DropdownMenu.Root>
                        <DropdownMenu.Trigger asChild>
                            <button className="absolute top-2 right-2 w-8 h-8 flex items-center justify-center bg-white rounded-full shadow hover:bg-neutral-100 p-0">
                                <span className="flex items-center justify-center w-full h-full">
                                    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <circle cx="4" cy="10" r="1.2" fill="#888" />
                                        <circle cx="10" cy="10" r="1.2" fill="#888" />
                                        <circle cx="16" cy="10" r="1.2" fill="#888" />
                                    </svg>
                                </span>
                            </button>
                        </DropdownMenu.Trigger>
                        <DropdownMenu.Content sideOffset={8} className="bg-white rounded-xl shadow-lg py-2 px-4 min-w-[220px] z-50">
                            {isCover ? (
                                <DropdownMenu.Item className="py-2 px-2 rounded-md cursor-pointer hover:bg-neutral-100" onSelect={() => {
                                    // Trigger hidden file input for cover image
                                    if (coverFileInputRef.current) coverFileInputRef.current.click();
                                }}>
                                    Modifier
                                </DropdownMenu.Item>
                            ) : (
                                <>
                                    {index !== undefined && index > 0 && (
                                        <DropdownMenu.Item className="py-2 px-2 rounded-md cursor-pointer hover:bg-neutral-100" onSelect={() => onMenuAction && onMenuAction('move-forward', index)}>
                                            Déplacer vers l&apos;avant
                                        </DropdownMenu.Item>
                                    )}
                                    <DropdownMenu.Item className="py-2 px-2 rounded-md cursor-pointer hover:bg-neutral-100" onSelect={() => onMenuAction && onMenuAction('set-cover', index)}>
                                        Choisir comme photo de couverture
                                    </DropdownMenu.Item>
                                    <DropdownMenu.Item className="py-2 px-2 rounded-md cursor-pointer hover:bg-neutral-100 text-red-600" onSelect={() => onMenuAction && onMenuAction('delete', index)}>
                                        Supprimer
                                    </DropdownMenu.Item>
                                </>
                            )}
                        </DropdownMenu.Content>
                    </DropdownMenu.Root>
                )}
            </div>
        );
    }
    // AddMoreCard: displays the dotted rectangle for adding more images
    function AddMoreCard({ onClick }: { onClick?: () => void }) {
        return (
            <div
                className="relative bg-white border-2 border-dashed border-neutral-300 rounded-lg w-full h-48 flex flex-col items-center justify-center cursor-pointer group"
                onClick={onClick}
            >
                <span className="text-3xl text-neutral-400 mb-1">+</span>
                <span className="text-xs text-neutral-500">En ajoute d&apos;autres</span>
            </div>
        );
    }

    // --- Render ---
    // If images are uploaded, show the grid UI; otherwise, show the upload prompt
    const hasImages = !!coverImage;
    // Menu action handler
    const handleMenuAction = (action: string, idx?: number) => {
        if (action === 'move-forward' && idx !== undefined) {
            const index = idx
            setAdditionalFiles((prev: File[]) => {
                if (index <= 0 || index >= prev.length) return prev
                const arr = [...prev]
                const tmp = arr[index - 1]
                arr[index - 1] = arr[index]
                arr[index] = tmp
                return arr
            })
            validateStep('add-images')
        } else if (action === 'set-cover' && idx !== undefined) {
            const index = idx;
            setAdditionalFiles((prev: File[]) => {
                if (!prev[index]) return prev;
                const arr = [...prev];
                const [newCover] = arr.splice(index, 1);
                // Add the current cover photo to additionalFiles
                if (coverFile) arr.unshift(coverFile);
                setCoverFile(newCover);
                return arr;
            });
            validateStep('add-images');
        } else if (action === 'delete' && idx !== undefined) {
            const index = idx
            setAdditionalFiles((prev: File[]) => prev.filter((_, i) => i !== index))
            validateStep('add-images')
        } else if (action === 'modifier') {
            alert('Replace image functionality coming soon!');
        }
    };

    // Keep coverImage in sync with context.coverFile
    useEffect(() => {
        if (coverImage) {
            URL.revokeObjectURL(coverImage.url)
        }
        if (coverFile) {
            const url = URL.createObjectURL(coverFile)
            setCoverImage({ file: coverFile, url })
        } else {
            setCoverImage(null)
        }
    }, [coverFile])

    // Keep additionalImages in sync with context.additionalFiles
    useEffect(() => {
        additionalImages.forEach(img => URL.revokeObjectURL(img.url))
        const imgs = additionalFiles.map(file => ({ file, url: URL.createObjectURL(file) }))
        setAdditionalImages(imgs)
    }, [additionalFiles])

    // Revalidate context-level image step after additionalFiles change
    useEffect(() => {
        validateStep('add-images')
    }, [additionalFiles])

    useEffect(() => {
        if (
            pendingModalClose &&
            additionalFiles.length === modalSelectedFiles.length &&
            additionalFiles.every((file, idx) => file === modalSelectedFiles[idx])
        ) {
            if (validateStep('add-images')) {
                setModalDirection('forward');
                setTimeout(() => {
                    if (modalSelectedFiles[0]) {
                        const file = modalSelectedFiles[0];
                        const url = URL.createObjectURL(file);
                        setCoverImage(prev => {
                            if (prev) {
                                URL.revokeObjectURL(prev.url);
                            }
                            return { file, url };
                        });
                    }
                    const additionalImgs = modalSelectedFiles.map(file => ({ file, url: URL.createObjectURL(file) }));
                    setAdditionalImages(additionalImgs);
                    setIsUploadModalOpen(false);
                    setModalSelectedFiles([]);
                    if (modalPreviewUrl) {
                        URL.revokeObjectURL(modalPreviewUrl);
                    }
                    setModalPreviewUrl(null);
                    setModalStep('cover');
                }, 0);
            }
            setPendingModalClose(false);
        }
    }, [pendingModalClose, additionalFiles, modalSelectedFiles, validateStep, modalPreviewUrl]);

    return (
        <CommonLayout params={{ stepIndex: "add-images" }}>
            {/* Removed summary banner; error feedback moved below header */}
            {hasImages ? (
                <div className="flex flex-col items-center justify-center min-h-[60vh] w-full py-[6rem]">
                    {/* Updated header */}
                    <h2 className="text-xl md:text-2xl font-bold text-neutral-900 text-left mb-6 w-full max-w-3xl">Et voila ! Est-ce que tout semble en order ?</h2>
                    {/* Main UI cover error feedback */}
                    {coverUploadError && (
                        <div className="my-2 flex items-center justify-center w-full" role="alert" aria-live="assertive">
                            <div className="flex items-center bg-red-50 border border-red-200 rounded px-4 py-3 shadow-sm w-full max-w-3xl">
                                <svg className="h-5 w-5 text-red-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <circle cx="12" cy="12" r="10" strokeWidth="2" />
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01" />
                                </svg>
                                <span className="text-sm text-red-700 font-medium">{coverUploadError}</span>
                            </div>
                        </div>
                    )}
                    {/* Main UI additional images error feedback */}
                    {additionalUploadError ? (
                        <div className="my-2 flex items-center justify-center w-full" role="alert" aria-live="assertive">
                            <div className="flex items-center bg-red-50 border border-red-200 rounded px-4 py-3 shadow-sm w-full max-w-3xl">
                                <svg className="h-5 w-5 text-red-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <circle cx="12" cy="12" r="10" strokeWidth="2" />
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01" />
                                </svg>
                                <span className="text-sm text-red-700 font-medium">{additionalUploadError}</span>
                            </div>
                        </div>
                    ) : (formErrors.additionalFiles || fileError) && (
                        <div className="my-2 flex items-center justify-center w-full" role="alert" aria-live="assertive">
                            <div className="flex items-center bg-red-50 border border-red-200 rounded px-4 py-3 shadow-sm w-full max-w-3xl">
                                <svg className="h-5 w-5 text-red-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <circle cx="12" cy="12" r="10" strokeWidth="2" />
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01" />
                                </svg>
                                <span className="text-sm text-red-700 font-medium">{formErrors.additionalFiles || fileError}</span>
                            </div>
                        </div>
                    )}
                    {/* Image grid */}
                    <LayoutGroup>
                        <motion.div
                            className="w-full max-w-3xl mx-auto grid grid-cols-1 gap-6"
                            initial={{ opacity: 0, scale: 0.97 }}
                            animate={{ opacity: 1, scale: 1 }}
                            transition={{ duration: 0.5, ease: 'easeOut' }}
                        >
                            {/* Cover image full width */}
                            <motion.div layout key={coverImage.url}>
                                <ImageCard url={coverImage.url} alt="Photo de couverture" badge="Photo de couverture" menu isCover onMenuAction={handleMenuAction} />
                            </motion.div>
                            {/* Hidden file input for cover image replacement */}
                            <input
                                ref={coverFileInputRef}
                                type="file"
                                accept="image/png,image/jpeg"
                                className="hidden"
                                aria-label="Remplacer la photo de couverture"
                                onChange={handleCoverFileChange}
                            />
                            {/* Additional images grid */}
                            {additionalImages.length > 0 && (
                                <div className="grid grid-cols-2 gap-4">
                                    <AnimatePresence>
                                        {additionalImages.map((img, idx) => (
                                            <motion.div
                                                layout
                                                key={getFileKey(img.file)}
                                                initial={{ opacity: 0, scale: 0.8 }}
                                                animate={{ opacity: 1, scale: 1 }}
                                                exit={{ opacity: 0, scale: 0.8 }}
                                                transition={{ duration: 0.3 }}
                                            >
                                                <ImageCard url={img.url} alt={`Image supplémentaire ${idx + 1}`} menu index={idx} onMenuAction={handleMenuAction} />
                                            </motion.div>
                                        ))}
                                    </AnimatePresence>
                                    {/* Add more card after last image */}
                                    <AddMoreCard onClick={() => {
                                        if (addMoreFileInputRef.current) addMoreFileInputRef.current.click();
                                    }} />
                                    {/* Hidden file input for adding more images (main UI) */}
                                    <input
                                        ref={addMoreFileInputRef}
                                        type="file"
                                        accept="image/png,image/jpeg"
                                        className="hidden"
                                        aria-label="Ajouter des images supplémentaires"
                                        multiple
                                        onChange={handleAddMoreFileChange}
                                    />
                                </div>
                            )}
                            {/* If no additional images, show add more card alone */}
                            {additionalImages.length === 0 && <AddMoreCard onClick={() => {
                                if (addMoreFileInputRef.current) addMoreFileInputRef.current.click();
                            }} />}
                            {/* Hidden file input for adding more images (when no additional images) */}
                            {additionalImages.length === 0 && <input
                                ref={addMoreFileInputRef}
                                type="file"
                                accept="image/png,image/jpeg"
                                className="hidden"
                                aria-label="Ajouter des images supplémentaires"
                                multiple
                                onChange={handleAddMoreFileChange}
                            />}
                        </motion.div>
                    </LayoutGroup>
                </div>
            ) : (
                <div className="flex flex-col items-center justify-center pb-6 min-h-[60vh] w-full">
                    {/* Page header and description */}
                    <h2 className="text-xl md:text-2xl md:pt-12 font-bold text-neutral-900 text-center mb-3">
                        Vos photos mettent votre logement en valeur ? <br /> Faites-les briller sur notre plateforme !
                    </h2>
                    <p className="text-sm text-neutral-500 text-center mb-8 max-w-xl">
                        Une belle image vaut mille mots : montrez vos plus beaux espaces sous leur meilleur jour .
                    </p>
                    {/* Initial UI: Camera image and upload button */}
                    <div className="w-full max-w-xl border-2 border-dashed border-neutral-300 rounded-2xl bg-[#FAFAFB] flex flex-col items-center justify-center py-14 px-4 relative">
                        <div className="mb-8 flex items-center justify-center">
                            <Image
                                src="https://api.almindharbooking.com/storage/v1/object/public/add-listing//Camera.png"
                                alt="Camera"
                                width={180}
                                height={135}
                                className="object-contain"
                                priority
                            />
                        </div>
                        <button
                            type="button"
                            className="border border-[#EA580F] rounded-lg px-6 py-2 font-medium bg-white hover:bg-neutral-100 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-booking-orange"
                            onClick={() => setIsUploadModalOpen(true)}
                        >
                            Ajoutez des photos
                        </button>
                    </div>
                </div>
            )}
            {/* Modal and Blur Overlay for image upload */}
            <AnimatePresence>
                {isUploadModalOpen && (
                    <>
                        {/* Backdrop */}
                        <motion.div
                            className="fixed inset-0 z-40 bg-black/40 backdrop-blur-sm"
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                            exit={{ opacity: 0 }}
                            onClick={handleModalClose}
                        />
                        {/* Modal content extracted to a variable for reuse */}
                        {(() => {
                            const modalContent = (
                                <div ref={modalContentRef} className={`rounded-2xl px-4 ${modalStep === 'cover' ? 'h-auto overflow-y-hidden overflow-x-hidden' : 'h-full overflow-y-auto overflow-x-hidden'}`}>
                                    {/* Modal header and step title */}
                                    <div className="flex items-center mb-2">
                                        <button
                                            onClick={handleModalClose}
                                            aria-label="Fermer"
                                            className="flex items-center justify-center w-7 h-7 rounded-full bg-white border-[1px] border-black focus:outline-none hover:bg-neutral-100"
                                        >
                                            <X size={16} strokeWidth={2} color="black" />
                                        </button>
                                        <div className="flex-1 text-center">
                                            <h3 className="text-base font-semibold">
                                                {modalStep === 'cover' ? 'Photo de couverture' : 'Images supplémentaires'}
                                            </h3>
                                        </div>
                                    </div>
                                    <p className="text-xs text-neutral-500 text-center mb-4">
                                        {modalStep === 'cover'
                                            ? "Choisissez l'image qui représentera votre annonce."
                                            : "Ajoutez des photos supplémentaires."}
                                    </p>
                                    {/* Local error banner - cover upload */}
                                    {modalStep === 'cover' && coverUploadError && (
                                        <div className="my-2 flex items-center justify-center" role="alert" aria-live="assertive">
                                            <div className="flex items-center bg-red-50 border border-red-200 rounded px-1 py-1 shadow-sm w-full max-w-sm">
                                                <svg className="h-5 w-5 text-red-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <circle cx="12" cy="12" r="10" strokeWidth="2" />
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01" />
                                                </svg>
                                                <span className="text-sm text-red-700 font-medium">{coverUploadError}</span>
                                            </div>
                                        </div>
                                    )}
                                    {/* Local error banner - additional upload */}
                                    {modalStep === 'additional' && additionalUploadError && (
                                        <div className="my-2 flex items-center justify-center" role="alert" aria-live="assertive">
                                            <div className="flex items-center bg-red-50 border border-red-200 rounded px-1 py-1 shadow-sm w-full max-w-sm">
                                                <svg className="h-5 w-5 text-red-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <circle cx="12" cy="12" r="10" strokeWidth="2" />
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01" />
                                                </svg>
                                                <span className="text-sm text-red-700 font-medium">{additionalUploadError}</span>
                                            </div>
                                        </div>
                                    )}
                                    <AnimatePresence mode="wait" initial={false}>
                                        {modalStep === 'cover' && (
                                            <motion.div
                                                key="cover-step"
                                                initial={{ x: modalDirection === 'forward' ? '100%' : '-100%', opacity: 0 }}
                                                animate={{ x: 0, opacity: 1 }}
                                                exit={{ x: modalDirection === 'forward' ? '-100%' : '100%', opacity: 0 }}
                                                transition={{ type: 'tween', duration: 0.4 }}
                                            >
                                                <div className="flex flex-col h-full min-h-[400px]">
                                                    {/* Cover image upload area and preview */}
                                                    <div>
                                                        <div className="border-2 border-dashed border-neutral-300 rounded-xl flex flex-col items-center justify-center py-2 px-2 min-h-[140px] w-full">
                                                            <div className="w-full flex flex-col items-center">
                                                                {modalPreviewUrl ? (
                                                                    <div className="w-full h-40 relative rounded-lg overflow-hidden group">
                                                                        <Image
                                                                            src={modalPreviewUrl}
                                                                            alt={modalSelectedFiles[0]?.name || 'Image sélectionnée'}
                                                                            fill
                                                                            className="object-cover w-full h-full rounded-lg"
                                                                        />
                                                                        <div className="absolute inset-0 bg-black/40 flex items-center justify-center opacity-0 group-hover:opacity-100 group-focus-within:opacity-100 pointer-events-none group-hover:pointer-events-auto group-focus-within:pointer-events-auto transition-opacity duration-200 rounded-lg">
                                                                            <label htmlFor="modal-file-upload" className="cursor-pointer">
                                                                                <span className="inline-block rounded-lg px-6 py-2 font-medium" style={{ backgroundColor: '#EA580F', color: '#fff' }}>
                                                                                    Modifier
                                                                                </span>
                                                                                <input
                                                                                    id="modal-file-upload"
                                                                                    name="modal-file-upload"
                                                                                    type="file"
                                                                                    className="sr-only"
                                                                                    accept="image/png,image/jpeg"
                                                                                    onChange={handleModalFileChange}
                                                                                />
                                                                            </label>
                                                                        </div>
                                                                    </div>
                                                                ) : (
                                                                    <>
                                                                        <div className="mb-4">
                                                                            <Image
                                                                                src="https://api.almindharbooking.com/storage/v1/object/public/add-listing//Images.png"
                                                                                alt="Upload Placeholder"
                                                                                width={72}
                                                                                height={72}
                                                                                className="object-contain"
                                                                            />
                                                                        </div>
                                                                        <label htmlFor="modal-file-upload" className="w-full flex justify-center">
                                                                            <input
                                                                                id="modal-file-upload"
                                                                                name="modal-file-upload"
                                                                                type="file"
                                                                                className="sr-only"
                                                                                accept="image/png,image/jpeg"
                                                                                onChange={handleModalFileChange}
                                                                            />
                                                                            <span
                                                                                className="inline-block rounded-lg px-6 py-2 font-medium cursor-pointer transition-colors duration-200"
                                                                                style={{ backgroundColor: '#111', color: '#fff' }}
                                                                            >
                                                                                Parcourir
                                                                            </span>
                                                                        </label>
                                                                    </>
                                                                )}
                                                            </div>
                                                        </div>
                                                    </div>
                                                    {/* Modal action button */}
                                                    <div className="flex justify-end mt-6">
                                                        <button
                                                            onClick={handleModalSend}
                                                            disabled={!modalPreviewUrl}
                                                            className={`px-4 py-2 rounded-lg font-medium transition-colors duration-200 ${modalPreviewUrl ? '' : 'bg-neutral-200 text-neutral-400 cursor-not-allowed'}`}
                                                            style={modalPreviewUrl ? { backgroundColor: '#EA580F', color: '#fff' } : {}}
                                                        >
                                                            Suivant
                                                        </button>
                                                    </div>
                                                </div>
                                            </motion.div>
                                        )}
                                        {modalStep === 'additional' && (
                                            <motion.div
                                                key="additional-step"
                                                initial={{ x: modalDirection === 'forward' ? '100%' : '-100%', opacity: 0 }}
                                                animate={{ x: 0, opacity: 1 }}
                                                exit={{ x: modalDirection === 'back' ? '100%' : '100%', opacity: 0 }}
                                                transition={{ type: 'tween', duration: 0.4 }}
                                            >
                                                <div className="flex flex-col h-full min-h-[400px]">
                                                    {/* Grid/content area (no scroll classes) */}
                                                    <div className="grid grid-cols-2 gap-4 w-full">
                                                        <AnimatePresence initial={false}>
                                                            {modalSelectedFiles.map((file, idx) => (
                                                                <motion.div
                                                                    key={getFileKey(file)}
                                                                    initial={{ opacity: 0, scale: 0.8 }}
                                                                    animate={{ opacity: 1, scale: 1 }}
                                                                    exit={{ opacity: 0, scale: 0.8 }}
                                                                    transition={{ duration: 0.3 }}
                                                                >
                                                                    <div className="relative bg-neutral-200 rounded-lg w-full h-32 overflow-hidden flex items-center justify-center">
                                                                        <Image
                                                                            src={URL.createObjectURL(file)}
                                                                            alt={file.name || `Image supplémentaire ${idx + 1}`}
                                                                            fill
                                                                            className="object-cover w-full h-full rounded-lg"
                                                                        />
                                                                        <button
                                                                            type="button"
                                                                            aria-label="Supprimer l'image"
                                                                            className="absolute top-2 right-2 text-red-500 hover:text-red-700"
                                                                            onClick={() => {
                                                                                // Remove image at idx
                                                                                const newFiles = [...modalSelectedFiles];
                                                                                newFiles.splice(idx, 1);
                                                                                setModalSelectedFiles(newFiles);
                                                                            }}
                                                                        >
                                                                            <TrashIcon className="h-4 w-4" />
                                                                        </button>
                                                                    </div>
                                                                </motion.div>
                                                            ))}
                                                        </AnimatePresence>
                                                        {/* Add cell at the end */}
                                                        <div className="relative bg-white border-2 border-dashed border-neutral-300 rounded-lg w-full h-32 flex items-center justify-center cursor-pointer group">
                                                            <label htmlFor="modal-additional-upload" className="w-full h-full flex items-center justify-center cursor-pointer">
                                                                <input
                                                                    id="modal-additional-upload"
                                                                    name="modal-additional-upload"
                                                                    type="file"
                                                                    className="sr-only"
                                                                    accept="image/png,image/jpeg"
                                                                    multiple
                                                                    onChange={handleAdditionalFileChange}
                                                                />
                                                                <span
                                                                    className="inline-block rounded-lg px-3 py-1 font-medium text-sm transition-colors duration-200 border border-[#EA580F] text-[#EA580F] group-hover:bg-[#EA580F] group-hover:text-white"
                                                                >
                                                                    Ajouter
                                                                </span>
                                                            </label>
                                                        </div>
                                                    </div>
                                                    {/* Sticky Footer: Retour and Terminer buttons */}
                                                    <div className="flex items-center justify-between gap-2 mt-auto pt-4">
                                                        <button
    type="button"
    onClick={handleModalBack}
    className="px-4 py-2 rounded-lg font-medium border border-neutral-300 text-neutral-700 bg-white hover:bg-neutral-100 transition-colors duration-200"
>
    Retour
</button>
                                                        <button
                                                            onClick={handleModalSend}
                                                            disabled={modalSelectedFiles.length < 5}
                                                            className={`px-4 py-2 rounded-lg font-medium transition-colors duration-200 ${modalSelectedFiles.length >= 5 ? '' : 'bg-neutral-200 text-neutral-400 cursor-not-allowed'}`}
                                                            style={modalSelectedFiles.length >= 5 ? { backgroundColor: '#EA580F', color: '#fff' } : {}}
                                                        >
                                                            Terminer
                                                        </button>
                                                    </div>
                                                </div>
                                            </motion.div>
                                        )}
                                    </AnimatePresence>
                                </div>
                            );
                            return <>
                                {/* Mobile Bottom Sheet */}
                                <motion.div
                                    className="fixed bottom-0 left-0 w-full z-50 rounded-t-2xl bg-white shadow-lg md:hidden"
                                    initial={{ y: "100%" }}
                                    animate={{ y: 0, maxHeight: modalStep === 'additional' ? '90vh' : '60vh' }}
                                    exit={{ y: "100%" }}
                                    transition={{ type: "spring", stiffness: 400, damping: 32, maxHeight: { duration: 0.5 } }}
                                    style={{ maxHeight: modalStep === 'additional' ? '90vh' : '60vh' }}
                                >
                                    <div className={modalStep === 'additional' ? "overflow-y-auto max-h-[82vh] pt-4 pb-6" : "overflow-y-auto max-h-[52vh] pt-4 pb-6"}>
                                        {modalContent}
                                    </div>
                                </motion.div>
                                {/* Desktop Modal */}
                                <motion.div
                                    className="fixed inset-0 z-50 flex items-center justify-center hidden md:flex"
                                    initial={{ opacity: 0, scale: 0.95 }}
                                    animate={{ opacity: 1, scale: 1 }}
                                    exit={{ opacity: 0, scale: 0.95 }}
                                    transition={{ type: 'spring', stiffness: 300, damping: 30 }}
                                >
                                    <motion.div
                                        className="bg-white rounded-lg shadow-lg w-full max-w-md mx-auto p-6 relative"
                                        animate={{ maxHeight: modalStep === 'additional' ? '40rem' : '28rem' }}
                                        style={{ maxHeight: modalStep === 'additional' ? '40rem' : '28rem', overflowY: 'auto' }}
                                        transition={{ maxHeight: { duration: 0.5 } }}
                                    >
                                        {modalContent}
                                    </motion.div>
                                </motion.div>
                            </>;
                        })()}
                    </>
                )}
            </AnimatePresence>
        </CommonLayout>
    );
}
