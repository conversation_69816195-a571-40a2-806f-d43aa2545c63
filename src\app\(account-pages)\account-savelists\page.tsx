"use client"

import { Card, CardContent } from "@/components/ui/card"
import Image from "next/image"
import Link from "next/link"
import { format } from "date-fns"
import { useState, useEffect } from "react"
import { Heart } from "lucide-react"
import { useUser } from '@/contexts/UserContext'
import { toast } from "react-toastify"

interface Listing {
  id: string
  title: string
  status: string
  address: string
  city_id: string
  host_id: string
  type_id: string
  latitude: number
  longitude: number
  num_beds: number
  num_guests: number
  state_id: string
  category_id: string
  created_at: string
  updated_at: string
  featured_image_url: string
}

interface WishlistItem {
  id: number
  created_at: string
  listing_id: string
  listings: Listing
}

interface LocalWishlistItem {
  id: string | number
  listing_id: string | number
  created_at: string
  listings?: Partial<Listing>
}

// Helper function to get local wishlist items from localStorage
const getLocalWishlistItems = (): LocalWishlistItem[] => {
  if (typeof window === 'undefined') return [];
  
  try {
    const wishlistString = localStorage.getItem('guestWishlist');
    const ids = wishlistString ? JSON.parse(wishlistString) : [];
    
    // Convert IDs to objects with created_at date
    return ids.map((id: string | number) => ({
      id,
      listing_id: String(id),
      created_at: new Date().toISOString(),
    }));
  } catch (e) {
    console.error('Error reading wishlist from localStorage:', e);
    return [];
  }
};

// Helper function to remove an item from localStorage
const removeLocalWishlistItem = (itemId: string | number): void => {
  if (typeof window === 'undefined') return;
  
  try {
    const wishlistString = localStorage.getItem('guestWishlist');
    const ids = wishlistString ? JSON.parse(wishlistString) : [];
    const newIds = ids.filter((id: string | number) => id !== itemId);
    localStorage.setItem('guestWishlist', JSON.stringify(newIds));
  } catch (e) {
    console.error('Error updating localStorage wishlist:', e);
  }
};

export default function WishlistPage() {
  const { userProfile, isLoading: userLoading } = useUser();
  const [wishlistItems, setWishlistItems] = useState<(WishlistItem | LocalWishlistItem)[]>([])
  const [localListingDetails, setLocalListingDetails] = useState<Record<string, Partial<Listing>>>({})
  const [isLoading, setIsLoading] = useState(true)

  // Load wishlist items based on authentication status
  useEffect(() => {
    if (userLoading) return;
    
    if (userProfile) {
      fetchWishlistItems();
    } else {
      // For non-authenticated users, get wishlist from localStorage
      const localItems = getLocalWishlistItems();
      setWishlistItems(localItems);
      
      // If there are items, fetch their details
      if (localItems.length > 0) {
        fetchLocalListingDetails(localItems);
      } else {
        setIsLoading(false); // No items to load, so we're done
      }
    }
  }, [userProfile, userLoading]);

  const fetchWishlistItems = async () => {
    setIsLoading(true)
    try {
      const response = await fetch("/api/wishlist")
      if (!response.ok) {
        throw new Error("Failed to fetch wishlist items")
      }
      const data = await response.json()
      setWishlistItems(data)
    } catch (error) {
      console.error("Error fetching wishlist:", error)
      toast.error("Failed to load wishlist items")
    }
    setIsLoading(false)
  }

  // Fetch listing details for localStorage items
  const fetchLocalListingDetails = async (items: LocalWishlistItem[]) => {
    setIsLoading(true);
    try {
      const details: Record<string, Partial<Listing>> = {};
      
      // Fetch details for each listing ID
      await Promise.all(
        items.map(async (item) => {
          try {
            // Use the correct API endpoint
            const response = await fetch(`/api/listing/${item.id}`);
            if (response.ok) {
              const data = await response.json();
              
              // The API response structure is {listing: {...}, hostUser: {...}}
              if (data.listing) {
                // Store the listing data with the ID as key
                details[String(item.id)] = data.listing;
              } else {
                console.error(`Unexpected API response structure for listing ${item.id}`);
                details[String(item.id)] = {
                  title: "Format de données incorrect",
                  address: "Contactez le support technique",
                  num_beds: 0,
                  num_guests: 0,
                  featured_image_url: "/placeholder.svg"
                };
              }
            } else {
              console.error(`Error fetching listing ${item.id}: API returned ${response.status}`);
              // Add placeholder data for failed fetches
              details[String(item.id)] = {
                title: "Propriété indisponible",
                address: "Information non disponible",
                num_beds: 0,
                num_guests: 0,
                featured_image_url: "/placeholder.svg"
              };
            }
          } catch (error) {
            console.error(`Error fetching listing ${item.id}`);
            // Add placeholder data for failed fetches
            details[String(item.id)] = {
              title: "Erreur de chargement",
              address: "Impossible de récupérer les détails",
              num_beds: 0,
              num_guests: 0,
              featured_image_url: "/placeholder.svg"
            };
          }
        })
      );
      
      setLocalListingDetails(details);
    } catch (error) {
      console.error("Error fetching listing details");
    } finally {
      setIsLoading(false);
    }
  }

  const handleUnlike = async (item: WishlistItem | LocalWishlistItem) => {
    // Handle database items (authenticated users)
    if ('id' in item && typeof item.id === 'number' && userProfile) {
      try {
        const response = await fetch(`/api/wishlist?itemId=${item.id}`, {
          method: "DELETE",
        })
        if (!response.ok) {
          throw new Error("Failed to remove item from wishlist")
        }
        setWishlistItems(wishlistItems.filter((i) => i.id !== item.id))
      } catch (error) {
        console.error("Error removing item from wishlist:", error)
        toast.error("Failed to remove item from wishlist")
      }
    } 
    // Handle localStorage items (non-authenticated users)
    else {
      removeLocalWishlistItem(item.id);
      setWishlistItems(wishlistItems.filter((i) => i.id !== item.id));
    }
  }

  // Get listing details either from the API response or from local details
  const getListingDetails = (item: WishlistItem | LocalWishlistItem): Partial<Listing> => {
    // For database items (WishlistItem)
    if ('listings' in item && item.listings) {
      return item.listings;
    } 
    
    // For localStorage items, get details from our fetched details
    const id = String(item.id);
    const details = localListingDetails[id];
    
    if (details) {
      // Make sure the object has all needed properties
      return {
        ...details,
        title: details.title || "Sans titre",
        address: details.address || "Adresse non disponible",
        num_beds: details.num_beds || 0,
        num_guests: details.num_guests || 0,
        featured_image_url: details.featured_image_url || "/placeholder.svg"
      };
    }
    
    // Fallback if no details are found
    return {
      title: "Chargement...",
      address: "Chargement...",
      num_beds: 0,
      num_guests: 0,
      featured_image_url: "/placeholder.svg"
    };
  }

  // Better loading indicator
  if (isLoading && userLoading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[300px]">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-booking-orange"></div>
        <p className="mt-4 text-sm text-gray-500">Chargement des listes de favoris...</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-semibold mb-6">Listes de favoris</h2>
        <div className="flex gap-6 border-b">
          <button className="px-4 py-2 text-sm font-medium text-primary border-b-2 border-primary">Séjours</button>
        </div>
      </div>
      <div className="w-14 border-b border-neutral-200 dark:border-neutral-700"></div>
      
      {!userProfile && (
        <div className="mb-4 p-4 bg-yellow-50 border border-yellow-200 rounded-md">
          <p className="text-sm text-yellow-800">
            Vous consultez votre liste de favoris invité. <Link href="/login" className="text-booking-orange underline">Connectez-vous</Link> pour sauvegarder ces propriétés sur votre compte.
          </p>
        </div>
      )}
      
      {wishlistItems.length === 0 ? (
        <p className="text-muted-foreground">Votre liste de favoris est vide.</p>
      ) : (
        <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 mt-6">
          {wishlistItems.map((item) => {
            const listing = getListingDetails(item);
            // Fix the type check for listing_id property
            const listingId = 'listing_id' in item && item.listing_id ? item.listing_id : item.id;
            
            return (
              <Card key={item.id} className="group relative overflow-hidden hover:shadow-lg transition-all duration-300">
                <Link href={`/listing-stay-detail/${listingId}`}>
                  <div className="relative">
                    <button
                      className="absolute right-3 top-3 z-10 p-2 rounded-full bg-white/80 hover:bg-white"
                      onClick={(e) => {
                        e.preventDefault()
                        handleUnlike(item)
                      }}
                    >
                      <Heart className="w-5 h-5 text-red-500 fill-current" />
                    </button>
                    <div className="aspect-[4/3] relative overflow-hidden">
                      {!listing || isLoading ? (
                        <div className="w-full h-full bg-gray-200 animate-pulse flex items-center justify-center">
                          <span className="text-gray-400 text-sm">Chargement...</span>
                        </div>
                      ) : (
                        <Image
                          src={listing.featured_image_url || "/placeholder.svg"}
                          alt={listing.title || "Image de propriété"}
                          fill
                          className="object-cover transition-transform duration-300 group-hover:scale-110"
                          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                          unoptimized={!listing.featured_image_url?.startsWith('http')}
                        />
                      )}
                    </div>
                  </div>
                  <CardContent className="p-4">
                    <div className="space-y-2">
                      <div className="flex justify-between items-start">
                        <h3 className="font-medium line-clamp-1">
                          {!listing || isLoading ? (
                            <div className="h-5 w-3/4 bg-gray-200 animate-pulse rounded"></div>
                          ) : (
                            listing.title || "Propriété sans titre"
                          )}
                        </h3>
                      </div>
                      <p className="text-muted-foreground text-sm line-clamp-1">
                        {!listing || isLoading ? (
                          <div className="h-4 w-1/2 bg-gray-200 animate-pulse rounded"></div>
                        ) : (
                          listing.address || "Adresse non disponible"
                        )}
                      </p>
                      <div className="flex items-center gap-2 text-sm">
                        <span>{!listing || isLoading ? "..." : `${listing.num_beds || 0} lits`}</span>
                        <span className="text-muted-foreground">•</span>
                        <span>{!listing || isLoading ? "..." : `${listing.num_guests || 0} invités`}</span>
                      </div>
                      <div className="pt-2 flex items-center gap-1">
                        <span className="text-xs text-muted-foreground">
                          Ajouté le {format(new Date(item.created_at), "dd MMM yyyy")}
                        </span>
                      </div>
                    </div>
                  </CardContent>
                </Link>
              </Card>
            );
          })}
        </div>
      )}
    </div>
  )
}

