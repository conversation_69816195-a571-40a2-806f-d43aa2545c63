"use client"

import { useState, useEffect } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { SearchIcon, CreditCard } from "lucide-react"
import LoadingState from "./components/LoadingState"
import EmptyState from "./components/EmptyState"
import SubscriptionSearch from "./components/SubscriptionSearch"
import SubscriptionStats from "./components/SubscriptionStats"
import SubscriptionCard from "./components/SubscriptionCard"
import { toast } from "@/components/ui/use-toast"
import { calculateSubscriptionStats, type Subscription, isSubscriptionActive } from "./utils/status-helpers"

const AccountSubscriptionPage = () => {
    const [subscriptions, setSubscriptions] = useState<Subscription[]>([])
    const [filteredSubscriptions, setFilteredSubscriptions] = useState<Subscription[]>([])
    const [isLoading, setIsLoading] = useState(true)
    const [searchQuery, setSearchQuery] = useState("")

    // Fetch subscriptions data
    useEffect(() => {
        const fetchSubscriptions = async () => {
            try {
                setIsLoading(true)
                const response = await fetch("/api/account-subscription")

                if (!response.ok) {
                    throw new Error(`Error: ${response.status}`)
                }

                const data = await response.json()
                setSubscriptions(data.subscriptions)
                setFilteredSubscriptions(data.subscriptions)
            } catch (error) {
                console.error("Error fetching subscriptions:", error)
                toast({
                    title: "Erreur",
                    description: "Impossible de charger les abonnements. Veuillez réessayer.",
                    variant: "destructive",
                })
            } finally {
                setIsLoading(false)
            }
        }

        fetchSubscriptions()
    }, [])

    // Handle search filtering
    useEffect(() => {
        if (!subscriptions.length) return

        if (searchQuery.trim() === "") {
            setFilteredSubscriptions(subscriptions)
        } else {
            const query = searchQuery.toLowerCase()
            const filtered = subscriptions.filter(
                (subscription) =>
                    subscription.listings?.title?.toLowerCase().includes(query) ||
                    subscription.subscription_plan.toLowerCase().includes(query),
            )
            setFilteredSubscriptions(filtered)
        }
    }, [searchQuery, subscriptions])

    // Handle renew subscription
    const handleRenewSubscription = (subscription: Subscription) => {
        // This would navigate to renewal page or open a modal
        toast({
            title: "Fonction à venir",
            description: "Le renouvellement d'abonnement sera bientôt disponible.",
        })
    }

    // Handle update payment details
    const handleUpdatePaymentDetails = () => {
        toast({
            title: "Fonction à venir",
            description: "La mise à jour des détails de paiement sera bientôt disponible.",
        })
    }

    // Handle change to annual plan
    const handleChangeToAnnualPlan = () => {
        toast({
            title: "Fonction à venir",
            description: "Le changement de plan sera bientôt disponible.",
        })
    }

    // Get the active subscription (if any)
    const activeSubscription = subscriptions.find(isSubscriptionActive)

    // Compute statistics for the stats cards
    const stats = calculateSubscriptionStats(filteredSubscriptions)

    // Show loading state
    if (isLoading) {
        return (
            <div className="w-full max-w-5xl mx-auto">
                <h2 className="text-2xl font-semibold mb-6">Abonnement</h2>
                <LoadingState />
            </div>
        )
    }

    // If there's an active subscription, show the new design
    if (activeSubscription) {
        return (
            <div className="w-full max-w-5xl mx-auto">
                <h2 className="text-2xl font-semibold mb-6">Abonnement</h2>

                {/* Plan Section */}
                <div className="mb-8">
                    <h3 className="text-lg font-medium mb-4">Plan</h3>
                    <Card className="border-gray-200">
                        <CardContent className="p-6">
                            <div className="flex flex-col md:flex-row md:items-center md:justify-between">
                                <div>
                                    <div className="flex items-center justify-between md:justify-start">
                                        <h4 className="text-lg font-medium">{activeSubscription.subscription_plan || "Premium+"}</h4>
                                        <span className="ml-6 text-gray-700">{activeSubscription.payment_amount || "30"} TND (VAT)</span>
                                    </div>
                                    <div className="mt-2 text-gray-500">
                                        <p>{activeSubscription.is_trial ? "Essai" : "Mensuelle"}</p>
                                        <p className="mt-1">
                                            Next payment:{" "}
                                            {new Date(activeSubscription.end_date || "").toLocaleDateString("en-US", {
                                                month: "short",
                                                day: "numeric",
                                                year: "numeric",
                                            })}
                                        </p>
                                    </div>
                                </div>
                                <Button
                                    variant="link"
                                    className="text-blue-600 hover:text-blue-800 p-0 h-auto mt-4 md:mt-0"
                                    onClick={handleChangeToAnnualPlan}
                                >
                                    Changement au plan annuel
                                </Button>
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Payment Details Section */}
                <div>
                    <h3 className="text-lg font-medium mb-4">Payment details</h3>
                    <Card className="border-gray-200">
                        <CardContent className="p-6">
                            <div className="flex flex-col md:flex-row md:items-center md:justify-between">
                                <div className="flex items-center">
                                    <CreditCard className="h-5 w-5 mr-3 text-gray-500" />
                                    <span>578 •••• •••• 5755</span>
                                </div>
                                <Button
                                    variant="link"
                                    className="text-blue-600 hover:text-blue-800 p-0 h-auto mt-4 md:mt-0"
                                    onClick={handleUpdatePaymentDetails}
                                >
                                    Mettre à jour les détails de paiement
                                </Button>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>
        )
    }

    // Show empty state if no subscriptions at all
    if (!subscriptions.length) {
        return (
            <div className="w-full max-w-5xl mx-auto py-10">
                <h2 className="text-2xl font-semibold mb-6">Abonnement</h2>
                <EmptyState />
            </div>
        )
    }

    // Show empty search results state with search bar still visible
    if (!filteredSubscriptions.length && subscriptions.length > 0) {
        return (
            <div className="w-full max-w-5xl mx-auto py-10">
                <h2 className="text-2xl font-semibold mb-6">Abonnement</h2>

                {/* Search - kept visible */}
                <div className="mb-6">
                    <SubscriptionSearch searchQuery={searchQuery} setSearchQuery={setSearchQuery} />
                </div>

                <Card>
                    <CardContent className="p-8 flex flex-col items-center justify-center">
                        <div className="w-12 h-12 bg-blue-50 rounded-full flex items-center justify-center mb-4">
                            <SearchIcon className="w-6 h-6 text-[#2D5BFF]" />
                        </div>
                        <h3 className="text-base font-semibold mb-2">Aucun résultat trouvé</h3>
                        <p className="text-sm text-gray-500 text-center max-w-md">
                            Aucun abonnement ne correspond à votre recherche. Essayez d&apos;autres termes ou effacez la recherche.
                        </p>
                        {searchQuery && (
                            <Button variant="outline" className="mt-4" onClick={() => setSearchQuery("")}>
                                Effacer la recherche
                            </Button>
                        )}
                    </CardContent>
                </Card>
            </div>
        )
    }

    // Show the list of subscriptions (original design)
    return (
        <div className="w-full max-w-5xl mx-auto">
            <h2 className="text-2xl font-semibold mb-6">Abonnement</h2>

            {/* Search */}
            <div className="mb-6">
                <SubscriptionSearch searchQuery={searchQuery} setSearchQuery={setSearchQuery} />
            </div>

            {/* Statistics */}
            <SubscriptionStats stats={stats} />

            {/* Subscription cards */}
            <div className="grid grid-cols-1 gap-4">
                {filteredSubscriptions.map((subscription) => (
                    <SubscriptionCard key={subscription.id} subscription={subscription} onRenew={handleRenewSubscription} />
                ))}
            </div>
        </div>
    )
}

export default AccountSubscriptionPage
