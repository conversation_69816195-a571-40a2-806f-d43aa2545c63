import { NextRequest, NextResponse } from "next/server"
import { createClient } from "@/utils/supabase/server"
import { sendVerificationStatusEmail } from "@/utils/verification-notifications"

export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient()
    
    // Check if the user is authenticated and is an admin
    const {
      data: { user },
    } = await supabase.auth.getUser()
    
    if (!user) {
      return NextResponse.json(
        { error: "Vous devez être connecté pour effectuer cette action" },
        { status: 401 }
      )
    }
    
    // Check if user is admin
    const { data: adminData, error: adminError } = await supabase
      .from("profiles")
      .select("role")
      .eq("id", user.id)
      .single()
    
    if (adminError || !adminData || adminData.role !== "admin") {
      return NextResponse.json(
        { error: "Vous n&apos;avez pas les droits pour effectuer cette action" },
        { status: 403 }
      )
    }
    
    // Get request body
    const body = await request.json()
    const { verificationId, status, reason } = body
    
    if (!verificationId || !status) {
      return NextResponse.json(
        { error: "Identifiant de vérification et statut requis" },
        { status: 400 }
      )
    }
    
    // Validate status
    const validStatuses = ["pending", "approved", "rejected"]
    if (!validStatuses.includes(status)) {
      return NextResponse.json(
        { error: "Statut invalide. Les valeurs acceptées sont: pending, approved, rejected" },
        { status: 400 }
      )
    }
    
    // Get the verification record to find the user
    const { data: verification, error: verificationError } = await supabase
      .from("identity_verifications")
      .select("user_id, status")
      .eq("id", verificationId)
      .single()
    
    if (verificationError || !verification) {
      return NextResponse.json(
        { error: "Vérification non trouvée" },
        { status: 404 }
      )
    }
    
    // Update the verification status
    const { error: updateError } = await supabase
      .from("identity_verifications")
      .update({ status, updated_at: new Date().toISOString() })
      .eq("id", verificationId)
    
    if (updateError) {
      return NextResponse.json(
        { error: "Erreur lors de la mise à jour du statut" },
        { status: 500 }
      )
    }
    
    // If status is approved, update the user's profile to mark them as verified
    if (status === "approved") {
      await supabase
        .from("profiles")
        .update({ 
          is_verified: true,
          verified_at: new Date().toISOString()
        })
        .eq("id", verification.user_id)
    }
    
    // Send email notification
    await sendVerificationStatusEmail(
      verification.user_id,
      status as any,
      reason
    )
    
    return NextResponse.json({
      success: true,
      message: "Statut de vérification mis à jour avec succès"
    })
  } catch (error) {
    console.error("Error updating verification status:", error)
    return NextResponse.json(
      { error: "Une erreur s&apos;est produite lors de la mise à jour du statut" },
      { status: 500 }
    )
  }
}

// Get verification requests with pagination and filtering
export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient()
    
    // Check if the user is authenticated and is an admin
    const {
      data: { user },
    } = await supabase.auth.getUser()
    
    if (!user) {
      return NextResponse.json(
        { error: "Vous devez être connecté pour effectuer cette action" },
        { status: 401 }
      )
    }
    
    // Check if user is admin
    const { data: adminData, error: adminError } = await supabase
      .from("profiles")
      .select("role")
      .eq("id", user.id)
      .single()
    
    if (adminError || !adminData || adminData.role !== "admin") {
      return NextResponse.json(
        { error: "Vous n&apos;avez pas les droits pour effectuer cette action" },
        { status: 403 }
      )
    }
    
    // Get query parameters
    const url = new URL(request.url)
    const status = url.searchParams.get("status") || "pending"
    const page = parseInt(url.searchParams.get("page") || "1")
    const limit = parseInt(url.searchParams.get("limit") || "10")
    const from = (page - 1) * limit
    const to = from + limit - 1
    
    // Query verifications with user profile data
    let query = supabase
      .from("identity_verifications")
      .select(`
        id, 
        status, 
        created_at, 
        updated_at, 
        id_front_url, 
        id_back_url, 
        selfie_url,
        profiles:user_id (
          id,
          fullname,
          email,
          avatar_url
        )
      `, { count: "exact" })
    
    // Apply status filter if provided
    if (status !== "all") {
      query = query.eq("status", status)
    }
    
    // Apply pagination
    const { data, error, count } = await query
      .order("created_at", { ascending: false })
      .range(from, to)
    
    if (error) {
      return NextResponse.json(
        { error: "Erreur lors de la récupération des vérifications" },
        { status: 500 }
      )
    }
    
    return NextResponse.json({
      data,
      pagination: {
        page,
        limit,
        total: count || 0,
        pages: count ? Math.ceil(count / limit) : 0
      }
    })
  } catch (error) {
    console.error("Error fetching verification requests:", error)
    return NextResponse.json(
      { error: "Une erreur s&apos;est produite lors de la récupération des demandes" },
      { status: 500 }
    )
  }
}
