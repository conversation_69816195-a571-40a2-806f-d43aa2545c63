"use client"

import type React from "react"

import { useState, useEffect, type FC } from "react"
import DatePickerCustomHeaderTwoMonth from "@/components/DatePickerCustomHeaderTwoMonth"
import DatePicker from "react-datepicker"

export interface DatesCalendarProps {
    className?: string
    fieldClassName?: string
    onDateChange?: (startDate: Date | null, endDate: Date | null) => void
    renderDayContents?: (day: number, date: Date) => React.ReactNode
    blockedDates?: Date[]
    isOpenPicker?: boolean
    allowSelectingBlockedDates?: boolean
}

const DatesCalendar: FC<DatesCalendarProps> = ({
                                                               className = "[ lg:nc-flex-2 ]",
                                                               fieldClassName = "[ nc-hero-field-padding--small ]",
                                                               onDateChange,
                                                               renderDayContents,
                                                               blockedDates = [],
                                                               isOpenPicker = false,
                                                               allowSelectingBlockedDates = true,
                                                           }) => {
    const [dateRange, setDateRange] = useState<[Date | null, Date | null]>([null, null])

    useEffect(() => {
        if (dateRange[0] && dateRange[1]) {
            onDateChange && onDateChange(dateRange[0], dateRange[1])
            setDateRange([null, null])
        }
    }, [dateRange, onDateChange])

    const handleDateChange = (dates: [Date | null, Date | null]) => {
        setDateRange(dates)
    }

    const customDayContents = (day: number, date: Date) => {
        const isBlocked = blockedDates.some((blockedDate) => blockedDate.getTime() === date.getTime())
        const isInRange = dateRange[0] && dateRange[1] && date >= dateRange[0] && date <= dateRange[1]

        let className = "react-datepicker__day_span"
        if (isBlocked) className += " react-datepicker__day--disabled"
        if (isInRange) className += " bg-booking-orange"

        return <span className={className}>{renderDayContents ? renderDayContents(day, date) : day}</span>
    }

    return (
        <div className={`StayDatesRangeInput z-10 relative flex ${className}`}>
            <div
                className={`${isOpenPicker ? "" : "absolute left-1/2 z-10 mt-3 top-full w-screen max-w-sm -translate-x-1/2 transform px-4 sm:px-0 lg:max-w-3xl"}`}
            >
                <div
                    className={`${isOpenPicker ? "" : "overflow-hidden rounded-3xl shadow-lg ring-1 ring-black ring-opacity-5"}`}
                >
                    <DatePicker
                        selected={dateRange[0]}
                        onChange={handleDateChange}
                        startDate={dateRange[0]}
                        endDate={dateRange[1]}
                        selectsRange
                        monthsShown={2}
                        showPopperArrow={false}
                        inline
                        renderCustomHeader={(p) => <DatePickerCustomHeaderTwoMonth {...p} />}
                        renderDayContents={customDayContents}
                        excludeDates={allowSelectingBlockedDates ? undefined : blockedDates}
                    />
                </div>
            </div>
        </div>
    )
}

export default DatesCalendar
