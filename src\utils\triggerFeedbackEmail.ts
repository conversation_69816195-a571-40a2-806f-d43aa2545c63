import { createClient } from '@/utils/supabase/server';
import { StayFeedbackEmail } from '@/components/emails/StayFeedbackEmail';
import { renderAsync } from '@react-email/components';
import { emailService } from '@/utils/emailService';
import { shouldSendEmail, logSkippedEmail } from '@/utils/emailPreferences';

/**
 * Triggers a feedback email to the user after a stay.
 * 
 * @param bookingId The ID of the booking to trigger the feedback email for.
 * @returns True if the email was sent successfully, false otherwise.
 */
export async function triggerFeedbackEmail(bookingId: string) {
    const supabase = await createClient();

    // Step 1: Get booking details with listing information only
    const { data: booking, error: bookingError } = await supabase
        .from('bookings')
        .select(`
            *,
            listing:listings(
                title,
                featured_image_url,
                    city:cities(
        name
    )
            )
        `)
        .eq('id', bookingId)
        .single();

    if (bookingError || !booking) {
        console.error('Error fetching booking:', bookingError);
        return false;
    }

    // Step 2: Try to get user email and name from profiles
    const { data: userData, error: userError } = await supabase
        .from('profiles')
        .select('email, fullname')
        .eq('id', booking.user_id)
        .single();

    // If we couldn't get the user email, we can't send the feedback email
    if (userError || !userData || !userData.email) {
        console.error('Error fetching user email:', userError);
        return false;
    }

    // Step 3: Check if user has opted in to receive booking updates
    const shouldSend = await shouldSendEmail(
        booking.user_id,
        'stay_feedback'
    );
    
    if (!shouldSend) {
        console.log(`User ${booking.user_id} has opted out of booking update emails`);
        
        // Log skipped email
        await logSkippedEmail(supabase, {
            user_id: booking.user_id,
            email: userData.email,
            email_type: 'stay_feedback',
            reference_id: bookingId
        });
        
        return true; // Return true to indicate successful handling (though email was skipped)
    }

    // Generate a unique token for this feedback request
    const token = crypto.randomUUID();
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 7); // Token expires in 7 days

    // Save the feedback token
    const { error: tokenError } = await supabase
        .from('feedback_tokens')
        .insert({
            booking_id: bookingId,
            token: token,
            expires_at: expiresAt.toISOString()
        });

    if (tokenError) {
        console.error('Error creating feedback token:', tokenError);
        return false;
    }

    try {
        // Render the email
        const emailHtml = await renderAsync(
            StayFeedbackEmail({
                booking: {
                    id: booking.id,
                    start_date: booking.start_date,
                    end_date: booking.end_date,
                    // Calculate number of nights
                    num_nights: Math.ceil((new Date(booking.end_date).getTime() - new Date(booking.start_date).getTime()) / (1000 * 60 * 60 * 24)),
                    listing: {
                        title: booking.listing.title,
                        featured_image_url: booking.listing.featured_image_url,
                        city: booking.listing.city || { name: '' } 
                    }
                },
                feedbackToken: token,
                language: 'fr',
                userName: userData.fullname || ''
            })
        );

        // Use emailService instead of direct fetch
        const response = await emailService.sendEmail({
            to: userData.email,
            subject: 'Comment s\'est passé votre séjour ?',
            html: emailHtml,
        });

        if (!response.success) {
            throw new Error(response.error || 'Failed to send email');
        }

        return true;
    } catch (error) {
        console.error('Error sending feedback email:', error);
        return false;
    }
}