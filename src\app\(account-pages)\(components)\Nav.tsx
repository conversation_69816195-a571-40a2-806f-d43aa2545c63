"use client"

import type { Route } from "@/routers/types"
import Link from "next/link"
import { usePathname } from "next/navigation"
import type React from "react"
import { useState, useEffect } from "react"
import { User, ShieldCheck, Bell, FileCheck, Receipt, Rocket, Menu, X } from "lucide-react"
import { cn } from "@/lib/utils"
import { useUser } from "@/contexts/UserContext"

type NavItem = {
  path: Route
  label: string
  icon: React.ReactNode
}

export const Nav = () => {
  const pathname = usePathname()
  const { userProfile } = useUser()
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)

  // Close mobile menu when route changes
  useEffect(() => {
    setIsMobileMenuOpen(false)
  }, [pathname])

  // Close mobile menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const sidebar = document.getElementById("sidebar")
      if (sidebar && !sidebar.contains(event.target as Node) && isMobileMenuOpen) {
        setIsMobileMenuOpen(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [isMobileMenuOpen])

  // Handle window resize
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth >= 1024) {
        setIsMobileMenuOpen(false)
      }
    }

    window.addEventListener("resize", handleResize)
    return () => {
      window.removeEventListener("resize", handleResize)
    }
  }, [])

  const navItems: NavItem[] = [
    {
      path: "/account",
      label: "Informations personnelles",
      icon: <User size={20} />,
    },
    {
      path: "/account-password",
      label: "Connexion et sécurité",
      icon: <ShieldCheck size={20} />,
    },
    {
      path: "/account-preferences",
      label: "Préférences",
      icon: <Bell size={20} />,
    },
    {
      path: "/account-identity",
      label: "Vérification d'identité",
      icon: <FileCheck size={20} />,
    },
    {
      path: "/account-billing",
      label: "Facturation",
      icon: <Receipt size={20} />,
    },
    {
      path: "/account-subscription",
      label: "Abonnement",
      icon: <Rocket size={20} />,
    },
  ]

  return (
      <>
        {/* Mobile menu toggle button */}
        <div className="lg:hidden fixed top-4 left-4 z-50">
          <button
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className="p-2 rounded-md bg-white shadow-md text-gray-700"
              aria-label={isMobileMenuOpen ? "Close menu" : "Open menu"}
          >
            {isMobileMenuOpen ? <X size={24} /> : <Menu size={24} />}
          </button>
        </div>

        {/* Sidebar overlay for mobile */}
        {isMobileMenuOpen && (
            <div
                className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
                onClick={() => setIsMobileMenuOpen(false)}
            />
        )}

        {/* Sidebar */}
        <aside
            id="sidebar"
            className={cn(
                "fixed top-0 left-0 h-full bg-white z-40 transition-transform duration-300 ease-in-out w-[480px] shadow-md",
                "lg:translate-x-0 lg:static lg:h-auto lg:shadow-none lg:border-r border-gray-200",
                isMobileMenuOpen ? "translate-x-0" : "-translate-x-full",
            )}
        >
          <div className="p-6">
            <h2 className="text-xl font-bold text-black mb-6">Paramètres du compte</h2>

            <nav className="space-y-1">
              {navItems.map((item) => {
                const isActive = pathname === item.path
                return (
                    <Link
                        key={item.path}
                        href={item.path}
                        className={cn(
                            "flex items-center gap-3 px-4 py-3 rounded-lg transition-colors",
                            isActive ? "bg-booking-orange/10 text-black" : "text-black hover:bg-[#FFF1EC]",
                        )}
                    >
                      <span className={cn("text-black opacity-70")}>{item.icon}</span>
                      <span className="font-medium">{item.label}</span>
                    </Link>
                )
              })}
            </nav>
          </div>
        </aside>
      </>
  )
}
