"use client"

import { useState, useEffect } from "react"
import { Card, CardContent } from "@/components/ui/card"
import InvoiceTable from "./components/InvoiceTable"
import InvoiceFilters from "./components/InvoiceFilters"
import LoadMoreButton from "./components/LoadMoreButton"
import ExportActions from "./components/ExportActions"
import InvoiceDetailModal from "./components/InvoiceDetailModal"
import { toast } from "@/components/ui/use-toast"
import { generateInvoicePDF, generateBulkInvoicePDF } from "./utils/pdf-generator"
import { generateInvoiceCSV } from "./utils/csv-generator"
import { useUser } from "@/contexts/UserContext"
import { AlertTriangle } from "lucide-react"

// Define types
type Invoice = {
  id: string
  invoice_number: string
  start_date: string
  end_date: string
  subscription_plan: string
  payment_amount: number
  listings: {
    id: string
    title: string
    status?: string
  }
  profile?: {
    fullname: string
    phone_number: string
    email: string
  }
  invoice_status?: "pending" | "active"
  invoice_ready?: boolean
  is_activated?: boolean
}

type PaginationInfo = {
  total: number
  offset: number
  limit: number
  hasMore: boolean
}

const AccountBillingPage = () => {
  // Get user profile from context
  const { userProfile } = useUser()

  // State for invoices and loading state
  const [invoices, setInvoices] = useState<Invoice[]>([])
  const [pagination, setPagination] = useState<PaginationInfo>({
    total: 0,
    offset: 0,
    limit: 5,
    hasMore: false,
  })
  const [isLoading, setIsLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")

  // State for the invoice detail modal
  const [selectedInvoice, setSelectedInvoice] = useState<Invoice | null>(null)
  const [isModalOpen, setIsModalOpen] = useState(false)

  // Function to fetch invoices
  const fetchInvoices = async (offset = 0, append = false, searchValue?: string) => {
    try {
      setIsLoading(true)
      const queryParams = new URLSearchParams({
        limit: pagination.limit.toString(),
        offset: offset.toString(),
      })

      // Use the provided searchValue if available, otherwise use the state
      const currentSearch = searchValue !== undefined ? searchValue : searchTerm

      if (currentSearch) {
        queryParams.append("search", currentSearch)
      }

      const response = await fetch(`/api/account-billing/invoices?${queryParams.toString()}`)

      if (!response.ok) {
        throw new Error("Failed to fetch invoices")
      }

      const data = await response.json()

      // Enhance invoices with user profile data from context
      const enhancedInvoices = enhanceInvoicesWithUserProfile(data.invoices)

      if (append) {
        setInvoices((prev) => [...prev, ...enhancedInvoices])
      } else {
        setInvoices(enhancedInvoices)
      }

      setPagination(data.pagination)
    } catch (error) {
      console.error("Error fetching invoices:", error)
      toast({
        title: "Erreur",
        description: "Impossible de charger les factures. Veuillez réessayer.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Function to enhance invoices with profile data from context
  const enhanceInvoicesWithUserProfile = (invoices: Invoice[]): Invoice[] => {
    if (!userProfile || invoices.length === 0) return invoices

    return invoices.map((invoice) => ({
      ...invoice,
      profile: {
        fullname: userProfile.fullname || "",
        email: userProfile.email || "",
        phone_number: userProfile.phone_number || "",
      },
    }))
  }

  // Initial load
  useEffect(() => {
    fetchInvoices()
  }, [])

  // Handle search
  const handleSearch = (term: string) => {
    setSearchTerm(term)
    fetchInvoices(0, false, term)
  }

  // Handle load more
  const handleLoadMore = () => {
    if (pagination.hasMore) {
      fetchInvoices(pagination.offset + pagination.limit, true)
    }
  }

  // Handle view invoice
  const handleViewInvoice = (invoice: Invoice) => {
    // Check if invoice is pending
    if (invoice.invoice_status === "pending") {
      toast({
        title: "Facture en attente",
        description: "Cette facture sera disponible lorsque l'hébergement sera approuvé par notre administration.",
      })
      return
    }

    // Ensure invoice has profile data before viewing
    const invoiceWithProfile = {
      ...invoice,
      profile: invoice.profile || {
        fullname: userProfile?.fullname || "",
        email: userProfile?.email || "",
        phone_number: userProfile?.phone_number || "",
      },
    }

    setSelectedInvoice(invoiceWithProfile)
    setIsModalOpen(true)
  }

  // Handle download invoice
  const handleDownloadInvoice = async (invoice: Invoice) => {
    // Check if invoice is pending
    if (invoice.invoice_status === "pending") {
      toast({
        title: "Facture en attente",
        description: "Cette facture sera disponible lorsque l'hébergement sera approuvé par notre administration.",
      })
      return
    }

    try {
      setIsLoading(true)

      // Ensure invoice has profile data before generating PDF
      const invoiceWithProfile = {
        ...invoice,
        profile: invoice.profile || {
          fullname: userProfile?.fullname || "",
          email: userProfile?.email || "",
          phone_number: userProfile?.phone_number || "",
        },
      }

      // Generate PDF
      const pdfBlob = await generateInvoicePDF(invoiceWithProfile)

      // Create a download link
      const url = URL.createObjectURL(pdfBlob)
      const link = document.createElement("a")
      link.href = url
      link.download = `facture-${invoice.invoice_number}.pdf`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      toast({
        title: "Téléchargement réussi",
        description: `Facture ${invoice.invoice_number} téléchargée.`,
      })
    } catch (error) {
      console.error("Error downloading invoice:", error)
      toast({
        title: "Erreur",
        description: "Impossible de télécharger la facture. Veuillez réessayer.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Handle export as PDF
  const handleExportPDF = async () => {
    if (invoices.length === 0) {
      toast({
        title: "Aucune facture",
        description: "Il n'y a aucune facture à exporter.",
        variant: "destructive",
      })
      return
    }

    try {
      setIsLoading(true)

      // Ensure all invoices have profile data
      const invoicesWithProfile = invoices.map((invoice) => ({
        ...invoice,
        profile: invoice.profile || {
          fullname: userProfile?.fullname || "",
          email: userProfile?.email || "",
          phone_number: userProfile?.phone_number || "",
        },
      }))

      // Filter out pending invoices before export
      const activeInvoices = invoicesWithProfile.filter((invoice) => invoice.invoice_status !== "pending")

      // Show message if no active invoices to export
      if (activeInvoices.length === 0) {
        toast({
          title: "Aucune facture active",
          description:
              "Il n'y a aucune facture active à exporter. Les factures en attente ne sont pas incluses dans l'export.",
          variant: "default",
        })
        setIsLoading(false)
        return
      }

      // Generate bulk PDF with only active invoices
      const pdfBlob = await generateBulkInvoicePDF(activeInvoices)

      // Create a download link
      const url = URL.createObjectURL(pdfBlob)
      const link = document.createElement("a")
      link.href = url
      link.download = `factures-${new Date().toISOString().split("T")[0]}.pdf`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      toast({
        title: "Export réussi",
        description: `${activeInvoices.length} facture(s) active(s) exportée(s) en PDF.`,
      })
    } catch (error) {
      console.error("Error exporting PDF:", error)
      toast({
        title: "Erreur",
        description: "Impossible d'exporter les factures en PDF. Veuillez réessayer.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Handle export as CSV
  const handleExportCSV = () => {
    if (invoices.length === 0) {
      toast({
        title: "Aucune facture",
        description: "Il n'y a aucune facture à exporter.",
        variant: "destructive",
      })
      return
    }

    try {
      setIsLoading(true)

      // Ensure all invoices have profile data
      const invoicesWithProfile = invoices.map((invoice) => ({
        ...invoice,
        profile: invoice.profile || {
          fullname: userProfile?.fullname || "",
          email: userProfile?.email || "",
          phone_number: userProfile?.phone_number || "",
        },
      }))

      // Filter out pending invoices before export
      const activeInvoices = invoicesWithProfile.filter((invoice) => invoice.invoice_status !== "pending")

      // Show message if no active invoices to export
      if (activeInvoices.length === 0) {
        toast({
          title: "Aucune facture active",
          description:
              "Il n'y a aucune facture active à exporter. Les factures en attente ne sont pas incluses dans l'export.",
          variant: "default",
        })
        setIsLoading(false)
        return
      }

      // Generate CSV with only active invoices
      const csvContent = generateInvoiceCSV(activeInvoices)

      // Create a download link
      const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" })
      const url = URL.createObjectURL(blob)
      const link = document.createElement("a")
      link.href = url
      link.download = `factures-${new Date().toISOString().split("T")[0]}.csv`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      toast({
        title: "Export réussi",
        description: `${activeInvoices.length} facture(s) active(s) exportée(s) en CSV.`,
      })
    } catch (error) {
      console.error("Error exporting CSV:", error)
      toast({
        title: "Erreur",
        description: "Impossible d'exporter les factures en CSV. Veuillez réessayer.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
      <div className="w-full max-w-6xl mx-auto">
        <div className="mb-6">
          <h2 className="text-2xl font-semibold mb-1">Facturation</h2>
        </div>

        {/* Informational Card */}
        {invoices.some((invoice) => invoice.invoice_status === "pending") && (
            <Card className="mb-6 bg-amber-50 border-amber-200">
              <CardContent className="p-4">
                <div className="flex items-start space-x-4">
                  <div className="mt-1">
                    <AlertTriangle className="h-5 w-5 text-amber-600" />
                  </div>
                  <div>
                    <h3 className="font-medium text-amber-800">Factures en attente d&apos;approbation</h3>
                    <p className="text-amber-700 text-sm mt-1">
                      Certaines de vos factures correspondent à des hébergements en attente d&apos;approbation par notre
                      équipe. Ces factures seront activées automatiquement dès que l&apos;hébergement sera approuvé.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
        )}

        <div className="bg-white rounded-lg shadow-sm overflow-hidden">
          <div className="p-4 sm:p-6 flex flex-col md:flex-row justify-between gap-4 border-b">
            <div className="w-full md:w-2/3">
              <InvoiceFilters onSearch={handleSearch} isLoading={isLoading} />
            </div>
            <div className="w-full md:w-1/3 flex justify-end">
              <ExportActions
                  onExportPDF={handleExportPDF}
                  onExportCSV={handleExportCSV}
                  isLoading={isLoading}
                  hasInvoices={invoices.length > 0}
              />
            </div>
          </div>

          <div>
            <InvoiceTable
                invoices={invoices}
                isLoading={isLoading}
                onViewInvoice={handleViewInvoice}
                onDownloadInvoice={handleDownloadInvoice}
            />
          </div>

          <div className="flex justify-center p-4 border-t">
            <LoadMoreButton hasMore={pagination.hasMore} isLoading={isLoading} onLoadMore={handleLoadMore} />
          </div>
        </div>

        {/* Invoice Detail Modal */}
        <InvoiceDetailModal
            invoice={selectedInvoice}
            isOpen={isModalOpen}
            onClose={() => setIsModalOpen(false)}
            onDownload={handleDownloadInvoice}
        />
      </div>
  )
}

export default AccountBillingPage
