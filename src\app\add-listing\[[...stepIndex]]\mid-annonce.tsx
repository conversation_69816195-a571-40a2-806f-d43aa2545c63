"use client";

import React from "react";
import CommonLayout from "./CommonLayout";
import Image from "next/image";

const PageAddListingMidAnnonce = () => {
    return (
        <CommonLayout params={{ stepIndex: "mid-annonce" }}>
            <div className="flex flex-col min-h-[70vh] max-h-screen overflow-auto h-full pb-[80px] md:pb-0 hide-scrollbar">
                {/* Custom styles for screens up to 820px */}
                <style jsx>{`
                    @media (max-width: 820px) {
                        .column-layout-820 {
                            display: block;
                        }
                        .row-layout-820 {
                            display: none;
                        }
                    }
                    @media (min-width: 821px) {
                        .column-layout-820 {
                            display: none;
                        }
                        .row-layout-820 {
                            display: flex;
                        }
                    }
                `}</style>
                {/* Mobile & Medium screens (up to 820px): Video first, then text */}
                <div className="w-full mb-6 column-layout-820">
                    <video
                        src="https://api.almindharbooking.com/storage/v1/object/public/videos//mid_annonce.mp4"
                        autoPlay
                        loop
                        muted
                        playsInline
                        width={800}
                        height={600}
                        className="w-full h-[400px] object-cover bg-white"
                        aria-label="Présentation intermédiaire de l'annonce"
                    />
                </div>
                <div className="w-full px-4 py-6 md:px-0 md:py-0 max-w-2xl mx-auto column-layout-820">
                    <span className="text-gray-500 text-base mb-2 block">Étape 2</span>
                    <h1 className="font-bold text-gray-900 leading-tight text-left text-[clamp(1.5rem,5vw,2.2rem)] mb-4">
                        Continuez la création de votre annonce
                    </h1>
                    <p className="text-gray-500 text-base">
                        Ajoutez les équipements, les images et une description détaillée pour rendre votre annonce plus attractive. Plus votre annonce est complète, plus elle attire de voyageurs.
                    </p>
                </div>
                {/* Desktop/Tablet (above 820px): two-column layout */}
                <div className="row-layout-820 md:flex-row items-center w-full gap-8 md:gap-8 xl:gap-32 mt-auto">
                    {/* Left column: Step label, heading, description */}
                    <div className="flex flex-col justify-center px-4 md:px-0 pl-4 md:pl-8 lg:pl-12 ml-0 md:ml-4 lg:ml-8 max-w-2xl">
                        <span className="text-gray-500 text-base mb-2">Étape 2</span>
                        <h1 className="font-bold text-gray-900 leading-tight text-left text-[clamp(1.5rem,5vw,2.2rem)] md:text-3xl lg:text-5xl mb-4">
                            Continuez la création de votre annonce
                        </h1>
                        <p className="text-gray-500 text-base md:text-lg">
                            Ajoutez les équipements, les images et une description détaillée pour rendre votre annonce plus attractive. Plus votre annonce est complète, plus elle attire de voyageurs.
                        </p>
                    </div>
                    {/* Right column: Video */}
                    <div className="flex-1 flex items-end justify-end h-full">
                        <video
                            src="https://api.almindharbooking.com/storage/v1/object/public/videos//mid_annonce.mp4"
                            autoPlay
                            loop
                            muted
                            playsInline
                            width={800}
                            height={600}
                            className="h-auto max-h-full object-contain"
                            aria-label="Présentation intermédiaire de l'annonce"
                        />
                    </div>
                </div>
            </div>
        </CommonLayout>
    );
};

export default PageAddListingMidAnnonce; 