{"datePicker": {"previousMonth": "Previous Month", "nextMonth": "Next Month", "months": {"long": "long", "numeric": "numeric"}}, "cardCategory": {"minutesDrive": "minutes drive"}, "collection": {"showMore": "Show more"}, "creditCardForm": {"cardNumber": "Card Number", "cardName": "Card Name", "expirationDate": "Expiration Date", "month": "Month", "year": "Year", "cvv": "CVV", "cvvInfo": "The CVV number is a 3-digit security code", "submit": "Submit", "cancel": "Cancel"}, "commentListing": {"reviewIn": "review in"}, "common": {"visa": "VISA", "mastercard": "MASTERCARD", "hoursShort": "h", "minutesShort": "m", "next": "Next", "previous": "Previous", "email": "Email", "password": "Password", "confirmPassword": "Confirm Password", "name": "Name", "firstName": "First Name", "lastName": "Last Name", "submit": "Submit", "login": "<PERSON><PERSON>", "register": "Register", "logout": "Logout", "search": "Search", "loading": "Loading...", "error": "An error occurred", "success": "Success!", "cancel": "Cancel", "save": "Save", "edit": "Edit", "delete": "Delete", "create": "Create", "update": "Update"}, "experiencesCard": {"person": "person", "ads": "ADS"}, "editListingModal": {"editListing": "Edit Listing", "title": "Title", "description": "Description", "propertyType": "Property Type", "selectPropertyType": "Select property type", "city": "City", "state": "State", "nightlyRate": "Nightly Rate", "placeType": "Place Type", "selectPlaceType": "Select place type", "paymentType": "Payment Type", "selectPaymentType": "Select payment type", "saveChanges": "Save changes", "propertyTypes": {"villa": "Villa", "house": "House", "apartment": "Apartment", "cabin": "Cabin"}, "placeTypes": {"entirePlace": "Entire place", "privateRoom": "Private room", "sharedRoom": "Shared room"}, "paymentTypes": {"instantBooking": "Instant Booking", "cashBased": "Cash Based"}}, "flightCard": {"tripTime": "Trip time", "transitTime": "Transit time", "nonstop": "Nonstop", "roundTrip": "round-trip"}, "footer": {"gettingStarted": "Getting started", "installation": "Installation", "releaseNotes": "Release Notes", "upgradeGuide": "Upgrade Guide", "browserSupport": "Browser Support", "editorSupport": "Editor Support", "explore": "Explore", "designFeatures": "Design features", "prototyping": "Prototyping", "designSystems": "Design systems", "pricing": "Pricing", "security": "Security", "resources": "Resources", "bestPractices": "Best practices", "support": "Support", "developers": "Developers", "learnDesign": "Learn design", "releases": "Releases", "community": "Community", "discussionForums": "Discussion Forums", "codeOfConduct": "Code of Conduct", "communityResources": "Community Resources", "contributing": "Contributing", "concurrentMode": "Concurrent Mode"}, "footerNav": {"explore": "Explore", "wishlists": "Wishlists", "login": "Log in", "menu": "<PERSON><PERSON>"}, "headerFilter": {"latestArticles": "Latest Articles 🎈", "viewAll": "View all"}, "invoiceDetails": {"invoice": "Invoice", "status": "Status", "billingFrom": "Billing From", "paymentDate": "Payment Date", "paymentFrom": "Payment from", "paymentTo": "Payment To", "propertyDetails": "Property Details", "invoiceDate": "Invoice Date", "dueDate": "Due Date", "priceDetails": "Price details", "basePrice": "Base Price", "serviceFee": "Service Fee", "vatServiceFee": "VAT Service Fee", "total": "Total", "securityFees": "Security fees", "downloadPdf": "Download Pdf", "completed": "Completed"}, "invoiceTable": {"invoice": "Invoice", "billingFrom": "Billing From", "status": "Status", "paymentDate": "Payment Date", "listings": "Listings", "amount": "Amount", "action": "Action", "noResults": "No results.", "previous": "Previous", "next": "Next", "invoiceDetails": "Invoice Details"}, "label": {"default": "Label"}, "messageHost": {"typeMessage": "Type your message here...", "sendMessage": "Send Message"}, "messageList": {"loadingMessages": "Loading messages..."}, "mapContainer": {"searchAsIMove": "Search as I move the map"}, "messageForm": {"typeMessage": "Type a message...", "send": "Send"}, "likeSaveBtns": {"share": "Share", "save": "Save"}, "modalSelectGuests": {"selectGuests": "Select Guests", "clear": "Clear", "save": "Save"}, "modalSelectDate": {"selectDate": "Select Date", "whenIsYourTrip": "When's your trip?", "clearDates": "Clear dates", "save": "Save"}, "ncInputNumber": {"defaultLabel": "Number"}, "newStayCard": {"night": "night"}, "nextBtn": {"next": "Next"}, "occupancyChart": {"occupancyAnalysis": "Occupancy Analysis", "weekly": "Weekly", "monthly": "Monthly", "annually": "Annually", "houseAnalysis": "House Analysis", "guestSegmentation": "Guest Segmentation", "stayDuration": "Stay Duration", "seasonAnalysis": "Season Analysis", "statistics": "Statistics", "viewsByCountry": "Views by country", "activeUsers": "Active users", "vsLastWeek": "VS LAST WEEK"}, "overviewStats": {"activeBookings": "Active Bookings", "totalBookings": "Total Bookings", "cancellations": "Cancellations", "totalRevenue": "Total Revenue"}, "paymentMethodCard": {"copyInfo": "Copy Info", "account": "Account", "number": "Number", "expiryDate": "Expiry Date", "routing": "Rout<PERSON> (ABA)", "accountNumber": "Account number", "currentBalance": "Current Balance", "withdraw": "Withdraw", "share": "Share", "delete": "Delete"}, "paymentStatusChart": {"paymentStatus": "Payment Status", "pendingPayment": "Pending Payment", "completedPayment": "Completed Payment", "failedPayment": "Failed Payment", "refundedAmount": "Refunded amount"}, "paymentStatusCharts": {"total": "Total", "completedPayment": "Completed Payment", "pendingPayment": "Pending Payment", "failedPayment": "Failed Payment", "refundedAmount": "Refunded amount", "noDataAvailable": "No data available"}, "postCardMeta": {"readMore": "Read more"}, "sectionBecomeAnAuthor": {"whyChooseUs": "Why did you choose us?", "description": "Accompanying us, you have a trip full of experiences. With Chisfis, booking accommodation, resort villas, hotels, private houses, apartments... becomes fast, convenient and easy.", "becomeAnAuthor": "Become an author"}, "sectionClientSay": {"goodNewsFromFarAway": "Good news from far away", "clientThoughts": "Let's see what people think of <PERSON><PERSON><PERSON><PERSON>"}, "sectionGridAuthorBox": {"top10Authors": "Top 10 author of the month", "ratingDescription": "Rating based on customer reviews", "showMore": "Show me more", "becomeAHost": "Become a host"}, "saleOffBadge": {"saleOff": "-10% today"}, "recentActivities": {"title": "Recent Activities", "viewMore": "View More"}, "propertyCardH": {"network": "Network", "family": "Family", "beds": "beds", "baths": "baths", "sqFt": "Sq. Fit"}, "sectionGridCategoryBox": {"exploreNearby": "Explore nearby", "discoverGreatPlaces": "Discover great places near where you live"}, "sectionSubscribe2": {"title": "Join our newsletter 🎉", "description": "Read and share new perspectives on just about any topic. Everyone's welcome.", "getMoreDiscount": "Get more discount", "getPremiumMagazines": "Get premium magazines", "enterYourEmail": "Enter your email"}, "sideNav": {"dashboard": "Dashboard", "bookings": "Bookings", "myProperties": "My Properties", "listingManagement": "Listing Management", "calendar": "Calendar", "guestManagement": "Guest Management", "financials": "Financials", "invoice": "Invoice", "paymentInfo": "Payment info", "paymentStatus": "Payment Status", "help": "Help", "settings": "Settings"}, "sectionGridFeaturePlaces": {"featuredPlacesToStay": "Featured places to stay", "popularPlaces": "Popular places to stay that <PERSON><PERSON><PERSON><PERSON> recommends for you", "showMeMore": "Show me more"}, "sectionHowItWork": {"howItWork": "How it work", "keepCalmTravel": "Keep calm & travel on", "bookAndRelax": "Book & relax", "smartChecklist": "Smart checklist", "saveMore": "Save more", "enjoyTrip": "Let each trip be an inspirational journey, each room a peaceful space"}, "sectionOurFeatures": {"benefits": "Benefits", "happeningCities": "Happening cities", "costEffectiveAdvertising": "Cost-effective advertising", "advertisingDescription": "With a free listing, you can advertise your rental with no upfront costs", "reachMillions": "Reach millions with Chisfis", "reachDescription": "Millions of people are searching for unique places to stay around the world", "secureAndSimple": "Secure and simple", "secureDescription": "A Holiday Lettings listing gives you a secure and easy way to take bookings and payments online"}, "statsCard": {"date": "Date", "value": "Value"}, "stayCard": {"night": "night"}, "sectionVideos": {"theVideos": "🎬 The Videos", "videosDescription": "Check out our hottest videos. View more and share more new perspectives on just about any topic. Everyone's welcome."}, "sectionSliderNewCategories": {"suggestionsForDiscovery": "Suggestions for discovery", "popularPlaces": "Popular places to recommends for you"}, "startRating": {"reviews": "reviews"}, "tableOfContents": {"inThisArticle": "In This Article"}, "stayCardH": {"guests": "guests", "beds": "beds", "baths": "baths", "noSmoking": "No smoking", "bedrooms": "bedrooms", "wifi": "Wifi", "night": "night"}, "userNav": {"profile": "Profile", "settings": "Settings", "logOut": "Log out"}, "stayCard2": {"beds": "beds", "night": "night"}, "nextPrev": {"prev": "Prev", "next": "Next"}, "pagination": {"page": "Page"}, "tag": {"count": "count"}, "socialsShare": {"shareOn": "Share on"}, "socialsList": {"facebook": "Facebook", "twitter": "Twitter", "youtube": "Youtube", "instagram": "Instagram"}, "switchDarkMode": {"enableDarkMode": "Enable dark mode"}, "buttonThird": {"button": "<PERSON><PERSON>"}, "heading": {"sectionHeading": "Section Heading", "discoverOutstandingArticles": "Discover the most outstanding articles in all topics of life."}, "comment": {"reply": "Reply"}, "heading2": {"staysInTokyo": "Stays in Tokyo", "staysInfo": "233 stays · Aug 12 - 18 · 2 Guests"}, "fileUploader": {"uploadFile": "Upload a file", "dragAndDrop": "or drag and drop", "fileSizeLimit": "PNG, JPG, GIF up to 10MB", "fileTooLarge": "File is too large (maximum 10MB)."}, "buttonClose": {"close": "Close"}, "navigation": {"home": "Home", "about": "About", "contact": "Contact", "blog": "Blog", "profile": "Profile", "settings": "Settings"}, "auth": {"loginTitle": "Login to your account", "registerTitle": "Create a new account", "forgotPassword": "Forgot password?", "rememberMe": "Remember me", "dontHaveAccount": "Don't have an account?", "alreadyHaveAccount": "Already have an account?"}, "profile": {"personalInfo": "Personal Information", "updateProfile": "Update Profile", "changePassword": "Change Password", "deleteAccount": "Delete Account"}, "errors": {"required": "This field is required", "invalidEmail": "Please enter a valid email address", "passwordMismatch": "Passwords do not match", "weakPassword": "Password is too weak", "userNotFound": "User not found", "incorrectPassword": "Incorrect password", "emailAlreadyInUse": "Email is already in use"}, "addListing": {"step1": {"title": "Step 1: Basic Information", "description": "Let's start with the basics about your place.", "propertyType": "What type of place are you listing?", "propertyTypeOptions": {"apartment": "Apartment", "house": "House", "secondaryUnit": "Secondary unit", "uniqueSpace": "Unique space", "bedAndBreakfast": "Bed and breakfast", "boutiqueHotel": "Boutique hotel"}, "listingType": "What type of listing is this?", "listingTypeOptions": {"entirePlace": "An entire place", "privateRoom": "A private room", "sharedRoom": "A shared room"}, "location": "Where's your place located?", "locationPlaceholder": "Enter your address"}, "step2": {"title": "Step 2: Rooms and Spaces", "description": "Tell guests about the rooms and spaces in your listing.", "guestCount": "How many guests can your place accommodate?", "bedroomCount": "How many bedrooms are available to guests?", "bedCount": "How many beds can guests use?", "bathroomCount": "How many bathrooms are available to guests?"}, "step3": {"title": "Step 3: Amenities", "description": "Let guests know what your place has to offer.", "amenities": "What amenities do you offer?", "amenityOptions": {"wifi": "Wifi", "tv": "TV", "kitchen": "Kitchen", "washer": "<PERSON>her", "freeParking": "Free parking", "paidParking": "Paid parking", "airConditioning": "Air conditioning", "dedicatedWorkspace": "Dedicated workspace", "pool": "Pool", "hotTub": "Hot tub", "patio": "<PERSON><PERSON>", "bbqGrill": "BBQ grill", "outdoorDiningArea": "Outdoor dining area", "firepit": "Firepit", "gym": "Gym", "beachAccess": "Beach access", "lakeAccess": "Lake access", "skiInSkiOut": "Ski-in/Ski-out", "outdoorShower": "Outdoor shower"}}, "step4": {"title": "Step 4: Photos", "description": "Add some photos of your place.", "photoUpload": "Upload photos", "photoUploadDescription": "Drag and drop your photos here, or click to select files", "photoRequirements": "Photos should be at least 1024x683 pixels in size and in JPG or PNG format"}, "step5": {"title": "Step 5: Title and Description", "description": "Create a title and description for your listing.", "listingTitle": "Listing title", "listingTitlePlaceholder": "Enter a catchy title for your listing", "listingDescription": "Listing description", "listingDescriptionPlaceholder": "Describe your place to potential guests"}, "step6": {"title": "Step 6: Price", "description": "Set a price for your listing.", "basePrice": "Base price (per night)", "cleaningFee": "Cleaning fee", "additionalGuestFee": "Additional guest fee", "securityDeposit": "Security deposit"}, "step7": {"title": "Step 7: Booking Settings", "description": "Choose your booking settings.", "instantBook": "Allow instant booking", "minNights": "Minimum nights stay", "maxNights": "Maximum nights stay", "checkInTime": "Check-in time", "checkOutTime": "Check-out time"}, "step8": {"title": "Step 8: House Rules", "description": "Set house rules for your guests.", "rules": "House rules", "ruleOptions": {"noSmoking": "No smoking", "noPets": "No pets", "noParties": "No parties or events", "noUnregisteredGuests": "No unregistered guests", "quietHours": "Quiet hours", "checkInTime": "Check-in time", "checkOutTime": "Check-out time"}, "customRules": "Additional rules (optional)"}, "step9": {"title": "Step 9: Availability", "description": "Set your listing's availability.", "availabilityType": "How do you want to set your availability?", "availabilityTypeOptions": {"alwaysAvailable": "Always available", "unavailableByDefault": "Unavailable by default", "specificDates": "Available on specific dates"}, "availabilityCalendar": "Select available dates"}, "step10": {"title": "Step 10: Review and Submit", "description": "Review your listing details before submitting.", "reviewListing": "Review your listing", "termsAgreement": "By submitting this listing, you agree to our Terms of Service and Privacy Policy", "submitListing": "Submit Listing"}}, "AuthorPage": {"listingsTitle": "{name}'s listings", "listingsDescription": "{name}'s listings is very rich, 5 star reviews help him to be more branded.", "showMore": "Show me more", "reviewsTitle": "Reviews ({count} reviews)", "viewMoreReviews": "View {count} more reviews", "speaks": "Speaks {language}", "joinedIn": "Joined in {month} {year}"}, "UploadBlog": {"pageTitle": "Upload a New Blog", "uploadProgress": "Upload progress: {progress}%", "formLabels": {"title": "Title", "content": "Content", "excerpt": "Excerpt", "category": "Category", "tags": "Tags", "featuredImage": "Featured Image"}, "placeholders": {"title": "Enter blog title", "content": "Write your blog content here...", "excerpt": "Enter a brief excerpt", "category": "Select a category"}, "buttons": {"addFaq": "Add FAQ", "removeFaq": "Remove", "submit": "Submit", "submitting": "Submitting..."}, "faq": {"title": "FAQs", "questionPlaceholder": "Question", "answerPlaceholder": "Answer"}, "messages": {"imageProcessed": "Image processed successfully!", "imageProcessError": "Failed to process the image.", "uploadError": "Failed to upload the image.", "blogPostSuccess": "Blog post uploaded successfully!", "blogPostError": "Failed to submit the blog post."}}, "BlogPostContent": {"author": {"anonymous": "Anonymous"}, "buttons": {"like": "Like", "share": "Share", "comment": "Comment"}, "sections": {"faq": "Frequently Asked Questions", "comments": "Comments", "relatedPosts": "Related Posts"}, "placeholders": {"commentInput": "Add a comment..."}, "loading": "Loading..."}, "RelatedPosts": {"title": "Related Posts"}, "BlogCard": {"general": "General", "anonymous": "Anonymous"}, "Pagination1": {"pageLabel": "Page {number}"}, "RecommendedSection": {"title": "Recommended For You", "discover": "Discover"}, "CategorySortFilter": {"blogTitle": "Blog", "blogDescription": "Here, we share travel tips, destination guides, and stories that inspire your next adventure.", "all": "All", "sortBy": "Sort by :", "newest": "Newest", "oldest": "Oldest"}, "BlogFilter": {"all": "All"}, "BlogContent": {"loading": "Loading..."}, "HeroSection": {"title": "Explore the World with Us", "subtitle": "Discover travel tips, guides, and stories that inspire your next adventure.", "cta": "Start Your Journey"}, "Sidebar": {"newsletter": {"title": "Subscribe to almindhar booking newsletter", "placeholder": "Enter your email", "subscribe": "Subscribe"}, "helpCenter": {"title": "Help Centre", "guest": {"title": "Guest", "accountAccess": "Access and manage your account", "reservationHelp": "Help with a reservation"}, "host": {"title": "Host", "preparingToHost": "Preparing to host", "hostingHelp": "Help with hosting"}}, "cta": {"title": "Explore more to get your comfort zone", "subtitle": "Book your perfect stay with us.", "button": "Booking Now"}}, "CheckOutPage": {"title": "Confirm and pay", "yourStay": "Your stay", "viewBookingDetails": "View booking details", "date": "Date", "guests": "Guests", "adults": "Adults", "children": "Children", "payWith": "Pay with", "paypal": "<PERSON><PERSON>", "creditCard": "Credit card", "confirmAndPay": "Confirm and pay", "priceDetails": "Price details", "days": "days", "serviceFee": "Service fee", "total": "Total"}, "PageContact": {"title": "Contact", "address": "ADDRESS", "email": "EMAIL", "phone": "PHONE", "socialMedia": "SOCIAL MEDIA", "fullName": "Full name", "emailAddress": "Email address", "message": "Message", "sendMessage": "Send message"}, "PageLogin": {"title": "<PERSON><PERSON>", "continueWith": "Continue with", "or": "OR", "emailPlaceholder": "Email address", "passwordPlaceholder": "Password", "continue": "Continue", "newUser": "New user? Create an account"}, "MessagesPage": {"title": "Messages with User {userId}", "loginPrompt": "Please log in to view messages"}, "PayDonePage": {"paymentSuccessful": "Payment Successful!", "processingPayment": "Processing Payment...", "bookingConfirmed": "Your booking has been confirmed. You will receive a confirmation email shortly.", "processingMessage": "We're processing your payment. This may take a few moments.", "bookingDetails": "Booking Details", "bookingId": "Booking ID", "checkIn": "Check-in", "checkOut": "Check-out", "totalPrice": "Total Price", "viewMyBookings": "View My Bookings", "returnHome": "Return Home"}, "PrivatePage": {"greeting": "Hello {email}"}, "PageSignUp": {"title": "Sign Up", "emailLabel": "Email address", "emailPlaceholder": "<EMAIL>", "passwordLabel": "Password", "userTypeLabel": "Are you registering as:", "individual": "Individual", "business": "Business", "continue": "Continue", "alreadyHaveAccount": "Already have an account? Log in"}, "PageSubscription": {"title": "Subscription", "subtitle": "Pricing to suit businesses of all sizes.", "popular": "POPULAR", "month": "/month", "signUp": "Sign up", "plans": {"starter": {"name": "Starter", "features": ["Automated reports", "Faster processing", "Customizations"], "description": "Literally you probably haven't heard of them."}, "basic": {"name": "Basic", "features": ["Everything in Starter", "100 builds", "Progress reports", "Premium support"], "description": "Literally you probably haven't heard of them."}, "plus": {"name": "Plus", "features": ["Everything in Basic", "Unlimited builds", "Advanced analytics", "Company evaluations"], "description": "Literally you probably haven't heard of them."}}}, "AdminDashboard": {"title": "Admin Dashboard", "tabs": {"dashboard": "Dashboard", "properties": "Properties", "users": "Users", "reports": "Reports", "bookings": "Booking History"}, "metrics": {"totalIncome": "Total Income", "totalVisitors": "Total Visitors", "totalBookings": "Total Bookings", "revenue": "Revenue"}, "search": "Search...", "notifications": "Notifications", "addNewProperty": "Add New Property", "propertyStatus": {"all": "All Status", "active": "Active", "disapproved": "Disapproved", "pending": "Pending", "draft": "Draft"}, "propertyTable": {"property": "Property", "location": "Location", "status": "Status", "price": "Price", "host": "Host", "created": "Created", "actions": "Actions"}, "propertyActions": {"edit": "Edit Property", "viewDetails": "View Details", "markFeatured": "<PERSON> as Featured", "delete": "Delete Property", "approve": "Approve listing", "reject": "Reject listing"}, "propertyForm": {"editTitle": "Edit Property", "addTitle": "Add New Property", "editDescription": "Update the property information below.", "addDescription": "Fill out the form to add a new property.", "name": "Name", "location": "Location", "status": "Status", "price": "Price", "featured": "<PERSON> as Featured?", "cancel": "Cancel", "save": "Save"}, "users": {"title": "Users", "addUser": "Add User", "table": {"name": "Name", "email": "Email", "listings": "Listings", "status": "Status", "actions": "Actions"}, "editUser": "Edit User", "newPassword": "New Password", "passwordPlaceholder": "Leave blank if unchanged"}, "reports": {"title": "Reports", "period": {"weekly": "Weekly", "monthly": "Monthly", "yearly": "Yearly"}, "downloadReport": "Download Report", "metrics": {"totalRevenue": "Total Revenue", "bookings": "Bookings", "averageStay": "Average Stay", "occupancyRate": "Occupancy Rate"}, "charts": {"revenueOverview": "Revenue Overview", "bookingStatistics": "Booking Statistics"}}, "bookings": {"title": "Booking History", "search": "Search", "viewAll": "View All", "table": {"name": "Name", "bookingRef": "Booking Ref", "listings": "Listings", "checkIn": "Check in", "checkOut": "Check out", "status": "Status", "amount": "Amount", "action": "Action"}}, "cards": {"addCard": "Add Card"}, "common": {"edit": "Edit", "delete": "Delete"}}, "CarCard": {"seats": "seats", "perDay": "/day", "ads": "ADS"}, "CarCardH": {"seats": "seats", "autoGearbox": "Auto gearbox", "bags": "bags", "carOwner": "Car owner", "perDay": "/day"}, "CardAuthorBox": {"location": "New York"}, "BtnLikeIcon": {"title": "Save"}, "CardCategory1": {"articles": "Articles"}, "CardCategory3": {"properties": "properties"}, "CardCategory4": {"properties": "properties", "cars": "cars", "experiences": "experiences"}, "CardCategory5": {"properties": "properties"}, "CardCategory6": {"properties": "properties"}, "CategoryBadgeList": {"defaultColor": "blue"}}