'use client';

import React, { FC, useState, ReactNode } from 'react';

interface FileUploaderProps {
    label: string;
    onUploadComplete: (file: File | File[]) => void;
    multiple?: boolean;
    accept?: string;
    storeLocally?: boolean;
    customButton?: ReactNode;
    buttonClassName?: string;
    customWrapper?: ReactNode;
    hideDefaultUI?: boolean;
}

const FileUploader: FC<FileUploaderProps> = ({
    label,
    onUploadComplete,
    multiple = false,
    accept = 'image/*',
    storeLocally = false,
    customButton,
    buttonClassName,
    customWrapper,
    hideDefaultUI = false,
}) => {
    const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
    const [error, setError] = useState<string | null>(null);

    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const files = Array.from(e.target.files || []);
        setError(null);

        const validFiles = files.filter(file => {
            if (file.size > 10 * 1024 * 1024) {
                setError(`Le fichier ${file.name} est trop volumineux (maximum 10MB).`);
                return false;
            }
            return true;
        });

        setSelectedFiles(validFiles);

        if (storeLocally) {
            if (multiple) {
                onUploadComplete(validFiles);
            } else {
                onUploadComplete(validFiles[0]);
            }
        } else {
            // Implement Supabase upload logic here if needed
            console.warn('Supabase upload not implemented');
        }

        // Reset the input value to allow re-uploading the same file if needed
        e.target.value = '';
    };

    // Create the input element that will be used in both cases
    const inputElement = (
        <input
            id={`file-upload-${label}`}
            name={`file-upload-${label}`}
            type="file"
            className="sr-only"
            multiple={multiple}
            accept={accept}
            onChange={handleFileChange}
        />
    );

    // If we have a custom wrapper, render it with the input
    if (customWrapper) {
        return (
            <div>
                {label && (
                    <label htmlFor={`file-upload-${label}`} className="text-lg font-semibold">
                        {label}
                    </label>
                )}
                <label htmlFor={`file-upload-${label}`} className="cursor-pointer block">
                    {customWrapper}
                    {inputElement}
                </label>
                {error && <p className="mt-2 text-sm text-red-600">{error}</p>}
            </div>
        );
    }

    // Otherwise render the default UI
    return (
        <div>
            {label && (
                <label htmlFor={`file-upload-${label}`} className="text-lg font-semibold">
                    {label}
                </label>
            )}
            {!hideDefaultUI && (
                <div className="mt-5">
                    <label htmlFor={`file-upload-${label}`}>
                        <div className="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-neutral-300 dark:border-neutral-6000 border-dashed rounded-md focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-booking-orange">
                            <div className="space-y-1 text-center">
                                <svg
                                    className="mx-auto h-12 w-12 text-neutral-400"
                                    stroke="currentColor"
                                    fill="none"
                                    viewBox="0 0 48 48"
                                    aria-hidden="true"
                                >
                                    <path
                                        d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                                        strokeWidth="2"
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                    ></path>
                                </svg>
                                {customButton ? (
                                    <div className={buttonClassName}>
                                        {customButton}
                                        {inputElement}
                                    </div>
                                ) : (
                                    <div
                                        className={`relative cursor-pointer rounded-md font-medium text-primary-6000 hover:text-primary-500 ${buttonClassName}`}
                                    >
                                        <span className="text-dark-blue hover:text-booking-orange">Télécharger un fichier</span>
                                        {inputElement}
                                    </div>
                                )}
                                <p className="text-xs text-neutral-500 dark:text-neutral-400">
                                    PNG, JPG, GIF jusqu&apos;à 5MB
                                </p>
                            </div>
                        </div>
                    </label>
                </div>
            )}
            {customButton && hideDefaultUI && (
                <div className={buttonClassName}>
                    <label htmlFor={`file-upload-${label}`} className="cursor-pointer">
                        {customButton}
                        {inputElement}
                    </label>
                </div>
            )}
            {error && <p className="mt-2 text-sm text-red-600">{error}</p>}
        </div>
    );
};

export default FileUploader;
