import { createClient } from "@/utils/supabase/server";

/**
 * Email preference types that match the user preferences in the profiles table
 */
export type EmailPreferenceType = 'receiveNewsletter' | 'receiveBookingUpdates' | 'receivePromotions' | 'receiveSystemNotifications';

/**
 * Maps email types to preference categories
 */
const emailTypeToPreference: Record<string, EmailPreferenceType> = {
  // Newsletter emails
  'newsletter_welcome': 'receiveNewsletter',
  'newsletter_weekly': 'receiveNewsletter',
  
  // Booking updates
  'booking_reminder': 'receiveBookingUpdates',
  'reservation_request': 'receiveBookingUpdates',
  'stay_feedback': 'receiveBookingUpdates',
  
  // Promotional emails
  'similar_listings_instant': 'receivePromotions',
  'similar_listings_weekly': 'receivePromotions',
  'create_listing': 'receivePromotions',
  'wishlist_update': 'receivePromotions',
  'weekly_recommendations': 'receivePromotions',
  
  // System notifications
  'comment_update': 'receiveSystemNotifications',
  'reset_password': 'receiveSystemNotifications',
  'listing_approval': 'receiveSystemNotifications',
  'identity_verification': 'receiveSystemNotifications',
  'incomplete_profile': 'receiveSystemNotifications'
};

/**
 * Checks if a user has opted in to receive a specific type of email
 * 
 * @param userId The user ID to check preferences for
 * @param emailType The type of email being sent
 * @returns Boolean indicating if the email should be sent
 */
export async function shouldSendEmail(userId: string, emailType: string): Promise<boolean> {
  // If no user ID or email type mapping, default to sending the email
  if (!userId || !emailTypeToPreference[emailType]) {
    return true;
  }

  try {
    const supabase = await createClient();
    
    // Get the user's email preferences
    const { data, error } = await supabase
      .from('profiles')
      .select('email_preferences')
      .eq('id', userId)
      .single();
    
    if (error || !data?.email_preferences) {
      console.error('Error fetching user preferences or no preferences found:', error);
      // Default to sending if we can't determine preferences
      return true;
    }
    
    // Get the preference category for this email type
    const preferenceType = emailTypeToPreference[emailType];
    
    // Check if the user has opted in for this type of email
    return data.email_preferences[preferenceType] === true;
  } catch (error) {
    console.error('Error checking email preferences:', error);
    // Default to sending if there's an error
    return true;
  }
}

/**
 * Logs an email that was skipped due to user preferences
 * 
 * @param supabase Supabase client
 * @param params Log parameters
 */
export async function logSkippedEmail(
  supabase: any,
  params: {
    user_id: string;
    email: string;
    email_type: string;
    reference_id?: string;
  }
) {
  try {
    await supabase
      .from('email_logs')
      .insert({
        email_type: params.email_type,
        user_id: params.user_id,
        email: params.email,
        reference_listing_id: params.reference_id,
        status: 'skipped',
        reason: 'user_preferences',
        sent_at: new Date().toISOString()
      });
  } catch (error) {
    console.error('Failed to log skipped email:', error);
  }
}
