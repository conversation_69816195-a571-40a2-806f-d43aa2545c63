import { NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';

/**
 * GET - Fetch the identity verification status for the authenticated user
 */
export async function GET() {
  try {
    const supabase = await createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({ error: "User not authenticated" }, { status: 401 });
    }

    // Fetch the user's latest identity verification record
    const { data, error } = await supabase
      .from("identity_verifications")
      .select("status, rejection_reason, created_at, updated_at")
      .eq("user_id", user.id)
      .order("created_at", { ascending: false })
      .limit(1)
      .single();

    if (error) {
      // If no record found, return not_submitted status
      if (error.code === "PGRST116") {
        return NextResponse.json({ 
          status: "not_submitted",
          message: "No verification record found"
        });
      }
      
      console.error("Error fetching verification status:", error);
      return NextResponse.json({ 
        error: "Error fetching verification status: " + error.message 
      }, { status: 500 });
    }

    // Map database status to UI status for consistency
    let uiStatus = "not_submitted";
    if (data.status) {
      const dbStatus = data.status.toLowerCase();
      if (dbStatus === "pending") {
        uiStatus = "pending";
      } else if (dbStatus === "approuved" || dbStatus === "approved") {
        uiStatus = "verified";
      } else if (dbStatus === "declined" || dbStatus === "rejected") {
        uiStatus = "rejected";
      }
    }

    return NextResponse.json({
      dbStatus: data.status,
      status: uiStatus,
      rejection_reason: data.rejection_reason,
      created_at: data.created_at,
      updated_at: data.updated_at
    });
  } catch (error) {
    console.error("Verification status fetch error:", error);
    return NextResponse.json({ 
      error: "Error fetching verification status: " + (error instanceof Error ? error.message : String(error)) 
    }, { status: 500 });
  }
}
