import { createClient } from '@/utils/supabase/server';
import { initializeEmailService } from '@/utils/emailProviders';
import CommentUpdateEmail, { translations } from '../CommentUpdateEmail';
import { shouldSendEmail, logSkippedEmail } from '@/utils/emailPreferences';

interface CommentDetails {
    id: string;
    listing_id: string;
    user_id: string;
    rating: number;
    comment: string;
    created_at: string;
}

async function getListingWithHost(supabase: any, listing_id: string) {
    // First, get the listing and its host_id
    const { data: listing, error: listingError } = await supabase
        .from('listings')
        .select(`
            id,
            title,
            featured_image_url,
            host_id
        `)
        .eq('id', listing_id)
        .single();

    if (listingError) throw listingError;
    if (!listing) throw new Error('Listing not found');

    // Then, get the host's profile using the host_id
    const { data: hostProfile, error: profileError } = await supabase
        .from('profiles')
        .select('email')
        .eq('id', listing.host_id)
        .single();

    if (profileError) throw profileError;
    if (!hostProfile?.email) throw new Error('Host email not found');

    return {
        id: listing.id,
        title: listing.title,
        featured_image_url: listing.featured_image_url,
        host: {
            id: listing.host_id,
            email: hostProfile.email
        }
    };
}

async function getReviewerDetails(supabase: any, user_id: string) {
    const { data, error } = await supabase
        .from('profiles')
        .select('id, fullname')
        .eq('id', user_id)
        .single();

    if (error) throw error;
    return {
        id: data.id,
        full_name: data.fullname || 'Anonymous'
    };
}

async function logEmailAttempt(
    supabase: any,
    {
        user_id,
        email,
        listing_id,
        comment_id,
        status,
        error = null
    }: {
        user_id: string;
        email: string;
        listing_id: string;
        comment_id: string;
        status: 'success' | 'failed';
        error?: string | null;
    }
) {
    try {
        await supabase
            .from('email_logs')
            .insert({
                email_type: 'comment_update',
                user_id,
                email,
                reference_listing_id: listing_id,
                similar_listings: { comment_id },
                status,
                error,
                sent_at: status === 'success' ? new Date().toISOString() : null
            });
    } catch (error) {
        console.error('Failed to log email attempt:', error);
    }
}

export async function processCommentEmail(
    commentDetails: CommentDetails,
    action: 'new' | 'updated' = 'new'
) {
    const supabase = await createClient();
    const emailService = initializeEmailService();

    try {
        // 1. Get listing and host details
        const listing = await getListingWithHost(supabase, commentDetails.listing_id);
        
        // 2. Check if host has opted in to receive system notifications
        const shouldSend = await shouldSendEmail(
            listing.host.id,
            'comment_update'
        );
        
        if (!shouldSend) {
            console.log(`Host ${listing.host.id} has opted out of system notifications`);
            
            // Log skipped email
            await logSkippedEmail(supabase, {
                user_id: listing.host.id,
                email: listing.host.email,
                email_type: 'comment_update',
                reference_id: commentDetails.listing_id
            });
            
            return true; // Return true to indicate successful handling (though email was skipped)
        }

        // 3. Get reviewer details
        const reviewer = await getReviewerDetails(supabase, commentDetails.user_id);

        // 4. Prepare email data
        const baseUrl = process.env.NEXT_PUBLIC_APP_URL || '';
        const emailData = {
            listing: {
                id: listing.id,
                title: listing.title,
                imageUrl: listing.featured_image_url,
                url: `${baseUrl}/listing-stay-detail/${listing.id}`
            },
            comment: {
                rating: commentDetails.rating,
                comment: commentDetails.comment,
                created_at: commentDetails.created_at,
                reviewer_name: reviewer.full_name
            },
            action
        };

        // 5. Send email
        await emailService.sendEmail({
            to: listing.host.email,
            subject: translations.fr.subject[action],
            from: 'Almindhar Booking <<EMAIL>>',
            react: CommentUpdateEmail(emailData)
        });

        // 6. Log successful email
        await logEmailAttempt(supabase, {
            user_id: listing.host.id,
            email: listing.host.email,
            listing_id: listing.id,
            comment_id: commentDetails.id,
            status: 'success'
        });

        return true;
    } catch (error) {
        console.error('Error processing comment email:', error);

        // Log failed attempt if we have the necessary info
        if (error instanceof Error) {
            await logEmailAttempt(supabase, {
                user_id: commentDetails.user_id,
                email: 'unknown',
                listing_id: commentDetails.listing_id,
                comment_id: commentDetails.id,
                status: 'failed',
                error: error.message
            });
        }

        return false;
    }
}