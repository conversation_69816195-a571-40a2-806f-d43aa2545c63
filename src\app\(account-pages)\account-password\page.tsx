"use client"
import type React from "react"
import { useState } from "react"
import { createClient } from "@/utils/supabase/client"
import { useToast } from "@/hooks/use-toast"
import { cn } from "@/lib/utils"
import { Eye, EyeOff } from "lucide-react"

const supabase = createClient()

/**
 * Password validation rules
 * - Minimum 8 characters
 * - At least one uppercase letter
 * - At least one lowercase letter
 * - At least one number
 * - At least one special character
 */
const validatePassword = (password: string) => {
  const errors = []
  if (password.length < 8) {
    errors.push("Le mot de passe doit contenir au moins 8 caractères")
  }
  if (!/[A-Z]/.test(password)) {
    errors.push("Le mot de passe doit contenir au moins une lettre majuscule")
  }
  if (!/[a-z]/.test(password)) {
    errors.push("Le mot de passe doit contenir au moins une lettre minuscule")
  }
  if (!/[0-9]/.test(password)) {
    errors.push("Le mot de passe doit contenir au moins un chiffre")
  }
  if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    errors.push("Le mot de passe doit contenir au moins un caractère spécial")
  }
  return errors
}

const AccountPasswordPage = () => {
  // Form state
  const [currentPassword, setCurrentPassword] = useState("")
  const [newPassword, setNewPassword] = useState("")
  const [confirmPassword, setConfirmPassword] = useState("")

  // Error states
  const [currentPasswordError, setCurrentPasswordError] = useState<string | null>(null)
  const [newPasswordErrors, setNewPasswordErrors] = useState<string[]>([])
  const [confirmPasswordError, setConfirmPasswordError] = useState<string | null>(null)

  // Loading state
  const [isLoading, setIsLoading] = useState(false)

  // Password visibility states
  const [showCurrentPassword, setShowCurrentPassword] = useState(false)
  const [showNewPassword, setShowNewPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)

  const { toast } = useToast()

  /**
   * Handles the password update process
   * 1. Validates input
   * 2. Updates password through Supabase
   * 3. Shows success/error toast
   */
  const handleUpdatePassword = async (e: React.FormEvent) => {
    e.preventDefault()

    // Reset errors
    setCurrentPasswordError(null)
    setNewPasswordErrors([])
    setConfirmPasswordError(null)

    // Validate new password
    const passwordErrors = validatePassword(newPassword)
    if (passwordErrors.length > 0) {
      setNewPasswordErrors(passwordErrors)
      return
    }

    // Check if passwords match
    if (newPassword !== confirmPassword) {
      setConfirmPasswordError("Les mots de passe ne correspondent pas")
      return
    }

    try {
      setIsLoading(true)

      // First verify the current password
      const {
        data: { user },
        error: getUserError,
      } = await supabase.auth.getUser()
      if (getUserError) throw getUserError

      if (!user?.email) {
        throw new Error("Impossible de vérifier votre identité")
      }

      // Try to sign in with current password to verify it
      const { error: signInError } = await supabase.auth.signInWithPassword({
        email: user.email,
        password: currentPassword,
      })

      if (signInError) {
        setCurrentPasswordError("Le mot de passe actuel est incorrect")
        setIsLoading(false)
        return
      }

      // If current password is correct, proceed with update
      const { error: updateError } = await supabase.auth.updateUser({
        password: newPassword,
      })

      if (updateError) throw updateError

      // Clear form on success
      setCurrentPassword("")
      setNewPassword("")
      setConfirmPassword("")

      toast({
        title: "Succès",
        description: "Votre mot de passe a été mis à jour avec succès",
        variant: "default",
      })
    } catch (error: any) {
      console.error("Error updating password:", error)
      if (error.message === "Le mot de passe actuel est incorrect") {
        // This error is already handled above
        return
      }
      toast({
        title: "Erreur",
        description: "Une erreur est survenue lors de la mise à jour du mot de passe",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
      <div className="p-8">
        <h2 className="text-2xl font-medium text-black mb-8">Connexion et sécurité</h2>

        <div className="max-w-2xl">
          {/* Password Section */}
          <div>
            <h3 className="text-base font-medium mb-6">Mot de passe</h3>

            <form onSubmit={handleUpdatePassword} className="space-y-6">
              {/* Current Password */}
              <div>
                <label htmlFor="current-password" className="block text-sm font-medium mb-1">
                  Mot de passe actuel
                </label>
                <div className="relative">
                  <input
                      id="current-password"
                      type={showCurrentPassword ? "text" : "password"}
                      value={currentPassword}
                      onChange={(e) => setCurrentPassword(e.target.value)}
                      className={cn(
                          "w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-black",
                          currentPasswordError ? "border-red-500 focus:ring-red-500" : "",
                      )}
                      required
                      disabled={isLoading}
                  />
                  <button
                      type="button"
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500"
                      onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                  >
                    {showCurrentPassword ? <EyeOff size={18} /> : <Eye size={18} />}
                  </button>
                </div>
                {currentPasswordError && <p className="mt-1 text-sm text-red-500">{currentPasswordError}</p>}
              </div>

              <div>
                <p className="text-sm text-gray-500 mb-2">Besoin d&apos;un nouveau mot de passe ?</p>
              </div>

              {/* New Password */}
              <div>
                <label htmlFor="new-password" className="block text-sm font-medium mb-1">
                  Nouveau mot de passe
                </label>
                <div className="relative">
                  <input
                      id="new-password"
                      type={showNewPassword ? "text" : "password"}
                      value={newPassword}
                      onChange={(e) => setNewPassword(e.target.value)}
                      className={cn(
                          "w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-black",
                          newPasswordErrors.length > 0 ? "border-red-500 focus:ring-red-500" : "",
                      )}
                      required
                      disabled={isLoading}
                  />
                  <button
                      type="button"
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500"
                      onClick={() => setShowNewPassword(!showNewPassword)}
                  >
                    {showNewPassword ? <EyeOff size={18} /> : <Eye size={18} />}
                  </button>
                </div>
                {newPasswordErrors.length > 0 && (
                    <ul className="mt-1 text-sm text-red-500 list-disc list-inside">
                      {newPasswordErrors.map((error, index) => (
                          <li key={index}>{error}</li>
                      ))}
                    </ul>
                )}
              </div>

              {/* Confirm New Password */}
              <div>
                <label htmlFor="confirm-password" className="block text-sm font-medium mb-1">
                  Confirmer le nouveau mot de passe
                </label>
                <div className="relative">
                  <input
                      id="confirm-password"
                      type={showConfirmPassword ? "text" : "password"}
                      value={confirmPassword}
                      onChange={(e) => setConfirmPassword(e.target.value)}
                      className={cn(
                          "w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-black",
                          confirmPasswordError ? "border-red-500 focus:ring-red-500" : "",
                      )}
                      required
                      disabled={isLoading}
                  />
                  <button
                      type="button"
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500"
                      onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  >
                    {showConfirmPassword ? <EyeOff size={18} /> : <Eye size={18} />}
                  </button>
                </div>
                {confirmPasswordError && <p className="mt-1 text-sm text-red-500">{confirmPasswordError}</p>}
              </div>

              {/* Submit Button */}
              <button
                  type="submit"
                  disabled={isLoading || !currentPassword || !newPassword || !confirmPassword}
                  className="bg-black text-white px-4 py-2 rounded-md hover:bg-gray-800 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? "Mise à jour..." : "Mettre à jour le mot de passe"}
              </button>
            </form>
          </div>
        </div>
      </div>
  )
}

export default AccountPasswordPage
