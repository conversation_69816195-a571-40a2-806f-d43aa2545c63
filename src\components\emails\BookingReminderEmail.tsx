import { ReactElement } from 'react';
import { format, addDays } from 'date-fns';
import { fr, enUS, ar } from 'date-fns/locale';

interface BookingReminderEmailProps {
    booking: {
        id: string;
        listing: {
            title: string;
            address: string;
            imageUrl?: string;
            city?: {
                name: string;
            } | null;
            state?: {
                name: string;
            } | null;
        };
        start_date: string;
        end_date: string;
        total_price: number;
        num_guests: number;
        listing_pricing: {
            arrival_time: string;
            departure_time: string;
        };
    };
    language?: 'en' | 'fr' | 'ar';
    daysUntilTrip?: number;
}

const translations = {
    fr: {
        subject: 'Votre voyage est prévu dans {days} jours',
        preview: 'Votre séjour à {destination} approche',
        title: 'Votre voyage à {destination} est prévu dans {days} jours',
        ready: 'Êtes-vous prêt pour votre séjour dans {property} ?',
        cancellationTitle: 'Annulation gratuite',
        cancellationInfo: 'Vous pouvez annuler gratuitement jusqu\'au {date}.',
        property: 'Appartement',
        location: 'Emplacement',
        checkIn: 'Enregistrement',
        checkOut: 'Départ',
        confirmationNumber: 'Numéro de confirmation',
        manageReservation: 'Gérez votre réservation',
        canManage: 'Vous pouvez consulter, modifier ou annuler votre réservation gratuitement jusqu\'à {date}.',
        manageButton: 'Gérer la réservation',
        contactProperty: 'Contacter la propriété',
        cancelButton: 'Annuler la réservation',
        from: 'À partir de',
        to: 'à',
        until: 'jusqu\'à',
        price: 'Prix',
        currency: 'TND',
        for: 'pour',
        guests: 'invités',
        guest: 'invité',
        cancelReservation: 'Annuler la réservation'
    },
    en: {
        subject: 'Your trip is in {days} days',
        preview: 'Your stay in {destination} is approaching',
        title: 'Your trip to {destination} is in {days} days',
        ready: 'Are you ready for your stay at {property}?',
        cancellationTitle: 'Free Cancellation',
        cancellationInfo: 'You can cancel for free until {date}.',
        property: 'Apartment',
        location: 'Location',
        checkIn: 'Check-in',
        checkOut: 'Check-out',
        confirmationNumber: 'Confirmation number',
        manageReservation: 'Manage your reservation',
        canManage: 'You can view, modify or cancel your reservation for free until {date}.',
        manageButton: 'Manage reservation',
        contactProperty: 'Contact property',
        cancelButton: 'Cancel reservation',
        from: 'From',
        to: 'to',
        until: 'until',
        price: 'Price',
        currency: 'TND',
        for: 'for',
        guests: 'guests',
        guest: 'guest',
        cancelReservation: 'Cancel reservation'
    },
    ar: {
        subject: 'رحلتك بعد {days} أيام',
        preview: 'موعد إقامتك في {destination} يقترب',
        title: 'رحلتك إلى {destination} بعد {days} أيام',
        ready: 'هل أنت مستعد لإقامتك في {property}؟',
        cancellationTitle: 'الإلغاء المجاني',
        cancellationInfo: 'يمكنك الإلغاء مجانًا حتى {date}.',
        property: 'شقة',
        location: 'الموقع',
        checkIn: 'تسجيل الوصول',
        checkOut: 'تسجيل المغادرة',
        confirmationNumber: 'رقم التأكيد',
        manageReservation: 'إدارة الحجز الخاص بك',
        canManage: 'يمكنك عرض أو تعديل أو إلغاء حجزك مجانًا حتى {date}.',
        manageButton: 'إدارة الحجز',
        contactProperty: 'الاتصال بالعقار',
        cancelButton: 'إلغاء الحجز',
        from: 'من',
        to: 'إلى',
        until: 'حتى',
        price: 'السعر',
        currency: 'دينار تونسي',
        for: 'ل',
        guests: 'ضيف',
        guest: 'ضيف',
        cancelReservation: 'إلغاء الحجز'
    }
};

// Helper function to format dates based on locale
const formatDate = (dateString: string, locale: 'fr' | 'en' | 'ar') => {
    const date = new Date(dateString);
    const localeObj = locale === 'fr' ? fr : locale === 'ar' ? ar : enUS;
    
    // Format: "Vendredi, fév 21, 2025"
    return format(date, 'EEEE, MMM dd, yyyy', { locale: localeObj });
};

// Helper function to calculate days until trip
const calculateDaysUntilTrip = (startDate: string): number => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    const tripDate = new Date(startDate);
    tripDate.setHours(0, 0, 0, 0);
    
    const diffTime = tripDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    return diffDays;
};

// Helper function to replace placeholders in translation strings
const replacePlaceholders = (text: string, replacements: Record<string, string | number>) => {
    let result = text;
    Object.entries(replacements).forEach(([key, value]) => {
        result = result.replace(`{${key}}`, String(value));
    });
    return result;
};

export default function BookingReminderEmail({
    booking,
    language = 'fr',
    daysUntilTrip
}: BookingReminderEmailProps): ReactElement {
    const t = translations[language];
    const isRTL = language === 'ar';
    
    // Calculate days until trip
    const daysUntilTripCalculated = daysUntilTrip !== undefined ? daysUntilTrip : calculateDaysUntilTrip(booking.start_date);
    
    // Get destination from city or fallback to address
    const destination = booking.listing.city?.name || booking.listing.address.split(',')[0].trim();
    
    // Format dates
    const checkInDate = formatDate(booking.start_date, language);
    const checkOutDate = formatDate(booking.end_date, language);
    
    // Calculate free cancellation date (1 day before check-in)
    const cancelDate = new Date(booking.start_date);
    cancelDate.setDate(cancelDate.getDate() - 1);
    const freeCancelDate = formatDate(cancelDate.toISOString().split('T')[0], language);
    
    // Format times
    const arrivalTime = booking.listing_pricing?.arrival_time || '10:00';
    const departureTime = booking.listing_pricing?.departure_time || '12:00';
    
    // Base URL for actions
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'https://almindharbooking.com';
    
    // Create a custom email template without the orange and dark blue straps
    return (
        <html>
            <head>
                <meta name="viewport" content="width=device-width, initial-scale=1.0" />
                <meta name="color-scheme" content="light" />
                <meta name="supported-color-schemes" content="light" />
                <title>{replacePlaceholders(t.preview, { destination })}</title>
                <style>{`
                    body {
                        font-family: Arial, sans-serif;
                        line-height: 1.6;
                        color: #333;
                        margin: 0;
                        padding: 0;
                        direction: ${isRTL ? 'rtl' : 'ltr'};
                        background-color: #f5f5f5;
                        -webkit-text-size-adjust: 100%;
                        -ms-text-size-adjust: 100%;
                    }
                    
                    .container {
                        max-width: 1200px;
                        margin: 0 auto;
                        background-color: #ffffff;
                        padding: 0;
                        border-radius: 0;
                        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
                        width: 100%;
                    }
                    
                    .content {
                        padding: 40px 60px;
                    }
                    
                    .button {
                        display: inline-block;
                        padding: 14px 28px;
                        background-color: #0F172A;
                        color: white;
                        text-decoration: none;
                        border-radius: 6px;
                        font-weight: bold;
                        margin-right: 16px;
                        margin-bottom: 16px;
                        text-align: center;
                        transition: all 0.2s ease;
                    }
                    
                    .button:hover {
                        opacity: 0.9;
                        transform: translateY(-1px);
                    }
                    
                    .button-secondary {
                        background-color: #ffffff;
                        color: #0F172A;
                        border: 1px solid #0F172A;
                    }
                    
                    .button-danger {
                        background-color: #ffffff;
                        color: #EF4444;
                        border: 1px solid #EF4444;
                    }
                    
                    .property-card {
                        border: 1px solid #e5e5e5;
                        border-radius: 12px;
                        overflow: hidden;
                        margin-bottom: 40px;
                        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
                    }
                    
                    .property-image {
                        width: 825px;
                        height: 550px;
                        object-fit: cover;
                    }
                    
                    .property-details {
                        padding: 30px;
                    }
                    
                    .reservation-details {
                        display: flex;
                        flex-wrap: wrap;
                        margin: 0 -10px;
                    }
                    
                    .reservation-item {
                        flex: 1;
                        min-width: 250px;
                        padding: 15px;
                        margin: 10px;
                        background-color: #f9f9f9;
                        border-radius: 8px;
                    }
                    
                    .free-cancel-notice {
                        background-color: #E7FDE9;
                        border-radius: 8px;
                        padding: 20px 25px;
                        margin-bottom: 40px;
                        border-left: 4px solid #008009;
                    }
                    
                    .free-cancel-notice svg {
                        margin-right: 10px;
                        flex-shrink: 0;
                    }
                    
                    .free-cancel-text {
                        margin: 0;
                    }
                    
                    .action-buttons {
                        display: flex;
                        justify-content: space-between;
                        flex-wrap: wrap;
                        gap: 20px;
                        margin: 30px 0;
                    }
                    
                    @media only screen and (max-width: 768px) {
                        .content {
                            padding: 25px;
                        }
                        
                        .property-image {
                            height: 220px;
                        }
                        
                        .property-details {
                            padding: 20px;
                        }
                        
                        .reservation-item {
                            min-width: 100%;
                            margin: 5px 0;
                        }
                        
                        .action-buttons {
                            flex-direction: column;
                            gap: 10px;
                            justify-content: flex-start;
                        }
                        
                        .action-buttons a {
                            width: 100%;
                            flex: none;
                        }
                        
                        .button {
                            display: block;
                            margin-right: 0;
                            width: 100%;
                            padding: 12px 20px;
                        }
                        
                        table[width="100%"] td {
                            display: block;
                            width: 100% !important;
                            padding: 0 !important;
                            margin-bottom: 15px;
                        }
                        
                        table[width="100%"] td a {
                            max-width: none !important;
                        }
                    }
                `}</style>
            </head>
            <body>
                <div className="container">
                    {/* Logo */}
                    <div style={{ 
                        textAlign: 'left', 
                        padding: '30px 60px',
                        backgroundColor: '#fff',
                        borderBottom: '1px solid #f0f0f0'
                    }}>
                        <img 
                            src="https://api.almindharbooking.com/storage/v1/object/public/email-assets/Almindhar-booking-logo-2.png"
                            alt="Almindhar Booking"
                            style={{ height: '100px' }}
                        />
                    </div>
                    
                    <div className="content">
                        {/* Title */}
                        <h1 style={{
                            fontSize: '24px',
                            fontWeight: 'bold',
                            color: '#000000',
                            marginBottom: '15px',
                            textAlign: 'left'
                        }}>
                            {replacePlaceholders(t.title, { destination, days: daysUntilTripCalculated })}
                        </h1>
                        
                        {/* Ready question */}
                        <p style={{ 
                            fontSize: '16px', 
                            marginBottom: '25px', 
                            textAlign: 'left',
                            color: '#4B5563'
                        }}>
                            {replacePlaceholders(t.ready, { property: booking.listing.title })}
                        </p>
                        
                        {/* Cancellation notice */}
                        <table cellPadding={0} cellSpacing={0} border={0} style={{ 
                            width: '100%', 
                            backgroundColor: '#E7FDE9', 
                            borderRadius: '8px',
                            marginBottom: '40px',
                            borderLeft: '4px solid #008009'
                        }}>
                            <tr>
                                <td style={{ padding: '20px 25px' }}>
                                    <table cellPadding={0} cellSpacing={0} border={0} width="100%">
                                        <tr>
                                            <td width="24" valign="top" style={{ paddingRight: '10px' }}>
                                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <circle cx="12" cy="12" r="11" fill="#E7FDE9" stroke="#008009" strokeWidth="1.5"/>
                                                    <path d="M7 12L10.5 15.5L17 8" stroke="#008009" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                                </svg>
                                            </td>
                                            <td valign="top">
                                                <p style={{ 
                                                    fontWeight: 'bold', 
                                                    color: '#008009', 
                                                    margin: 0, 
                                                    fontSize: '16px',
                                                    paddingBottom: '5px'
                                                }}>
                                                    {t.cancellationTitle}
                                                </p>
                                                <p style={{ 
                                                    color: '#008009', 
                                                    fontSize: '14px', 
                                                    margin: 0,
                                                    lineHeight: '1.5'
                                                }}>
                                                    {replacePlaceholders(t.cancellationInfo, { date: freeCancelDate })}
                                                </p>
                                            </td>
                                        </tr>
                                    </table>
                                </td>
                            </tr>
                        </table>
                        
                        {/* Property Card */}
                        <table cellPadding={0} cellSpacing={0} border={0} style={{ 
                            width: '100%',
                            marginBottom: '40px',
                            borderRadius: '12px',
                            overflow: 'hidden',
                            border: '1px solid #e5e5e5',
                            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.05)'
                        }}>
                            {booking.listing.imageUrl && (
                                <tr>
                                    <td style={{ padding: 0 }}>
                                        <img 
                                            src={booking.listing.imageUrl}
                                            alt={booking.listing.title}
                                            style={{ 
                                                display: 'block',
                                                width: '100%', 
                                                maxWidth: '825px', 
                                                height: 'auto', 
                                                maxHeight: '400px', 
                                                objectFit: 'cover',
                                                margin: '0 auto'
                                            }}
                                        />
                                    </td>
                                </tr>
                            )}
                            <tr>
                                <td style={{ padding: '30px' }}>
                                    <h2 style={{ fontSize: '22px', margin: '0 0 10px 0', fontWeight: 'bold' }}>{booking.listing.title}</h2>
                                    <p style={{ fontSize: '16px', color: '#666', margin: '0 0 20px 0' }}>{booking.listing.address}</p>
                                    
                                    {/* Reservation Details */}
                                    <table cellPadding={0} cellSpacing={0} border={0} width="100%" style={{ marginBottom: '25px' }}>
                                        <tr>
                                            {/* Check-in */}
                                            <td width="50%" style={{ borderBottom: '1px solid #eee', paddingBottom: '20px', paddingRight: '15px' }}>
                                                <p style={{ fontSize: '16px', color: '#666', margin: '0 0 8px 0', fontWeight: 'bold' }}>{t.checkIn}</p>
                                                <p style={{ fontSize: '17px', margin: '0', fontWeight: '500' }}>{checkInDate}</p>
                                                <p style={{ fontSize: '15px', margin: '5px 0 0 0', color: '#666' }}>
                                                    {t.from} {arrivalTime}
                                                </p>
                                            </td>
                                            
                                            {/* Check-out */}
                                            <td width="50%" style={{ borderBottom: '1px solid #eee', paddingBottom: '20px', paddingLeft: '15px' }}>
                                                <p style={{ fontSize: '16px', color: '#666', margin: '0 0 8px 0', fontWeight: 'bold' }}>{t.checkOut}</p>
                                                <p style={{ fontSize: '17px', margin: '0', fontWeight: '500' }}>{checkOutDate}</p>
                                                <p style={{ fontSize: '15px', margin: '5px 0 0 0', color: '#666' }}>
                                                    {t.until} {departureTime}
                                                </p>
                                            </td>
                                        </tr>
                                    </table>
                                    
                                    {/* Price */}
                                    <table cellPadding={0} cellSpacing={0} border={0} width="100%" style={{ 
                                        backgroundColor: '#f9f9f9', 
                                        borderRadius: '8px',
                                        marginTop: '10px'
                                    }}>
                                        <tr>
                                            <td style={{ padding: '20px' }}>
                                                <table cellPadding={0} cellSpacing={0} border={0} width="100%">
                                                    <tr>
                                                        <td style={{ width: '70%' }}>
                                                            <p style={{ fontSize: '16px', color: '#666', margin: '0 0 5px 0' }}>{t.price}</p>
                                                            <p style={{ fontSize: '15px', margin: '0', color: '#666' }}>
                                                                {t.for} {booking.num_guests} {booking.num_guests > 1 ? t.guests : t.guest}
                                                            </p>
                                                        </td>
                                                        <td style={{ width: '30%', textAlign: 'right' }}>
                                                            <p style={{ fontSize: '24px', fontWeight: 'bold', margin: '0', color: '#0F172A' }}>
                                                                {booking.total_price} {t.currency}
                                                            </p>
                                                        </td>
                                                    </tr>
                                                </table>
                                            </td>
                                        </tr>
                                    </table>
                                </td>
                            </tr>
                        </table>
                        
                        {/* Action Buttons */}
                        <table width="100%" cellPadding={0} cellSpacing={0} border={0} style={{ marginTop: '30px', marginBottom: '30px' }}>
                            <tr>
                                <td width="32%" align="center" style={{ paddingRight: '2%' }}>
                                    <a 
                                        href={`${baseUrl}/bookings`} 
                                        style={{
                                            display: 'inline-block',
                                            padding: '14px 28px',
                                            backgroundColor: '#0F172A',
                                            color: 'white',
                                            textDecoration: 'none',
                                            borderRadius: '6px',
                                            fontWeight: 'bold',
                                            textAlign: 'center',
                                            width: '100%',
                                            maxWidth: '250px',
                                            fontSize: '16px',
                                            boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)'
                                        }}
                                    >
                                        {t.manageButton}
                                    </a>
                                </td>
                                <td width="32%" align="center" style={{ paddingLeft: '1%', paddingRight: '1%' }}>
                                    <a 
                                        href={`${baseUrl}/messages?booking=${booking.id}`} 
                                        style={{
                                            display: 'inline-block',
                                            padding: '14px 28px',
                                            backgroundColor: '#ffffff',
                                            color: '#0F172A',
                                            textDecoration: 'none',
                                            borderRadius: '6px',
                                            fontWeight: 'bold',
                                            textAlign: 'center',
                                            border: '1px solid #0F172A',
                                            width: '100%',
                                            maxWidth: '250px',
                                            fontSize: '16px'
                                        }}
                                    >
                                        {t.contactProperty}
                                    </a>
                                </td>
                                <td width="32%" align="center" style={{ paddingLeft: '2%' }}>
                                    <a 
                                        href={`${baseUrl}/bookings`} 
                                        style={{
                                            display: 'inline-block',
                                            padding: '14px 28px',
                                            backgroundColor: '#ffffff',
                                            color: '#EF4444',
                                            textDecoration: 'none',
                                            borderRadius: '6px',
                                            fontWeight: 'bold',
                                            textAlign: 'center',
                                            border: '1px solid #EF4444',
                                            width: '100%',
                                            maxWidth: '250px',
                                            fontSize: '16px'
                                        }}
                                    >
                                        {t.cancelReservation}
                                    </a>
                                </td>
                            </tr>
                        </table>
                        
                        {/* Manage Reservation Text */}
                        <p style={{ 
                            fontSize: '15px', 
                            color: '#666', 
                            textAlign: 'left',
                            margin: '15px 0 40px 0',
                            lineHeight: '1.6'
                        }}>
                            {replacePlaceholders(t.canManage, { date: freeCancelDate })}
                        </p>
                        
                        {/* Footer */}
                        <div className="footer" style={{ textAlign: 'left', borderTop: '1px solid #eee', paddingTop: '30px', marginTop: '40px' }}>
                            <p style={{ margin: '0 0 10px 0', fontSize: '14px', color: '#777' }}> 
                                &copy; 2024 Almindhar Booking. {language === 'fr' ? 'Tous droits réservés.' : language === 'en' ? 'All rights reserved.' : 'جميع الحقوق محفوظة.'}
                            </p>
                        </div>
                    </div>
                </div>
            </body>
        </html>
    );
}

export { type BookingReminderEmailProps, translations };