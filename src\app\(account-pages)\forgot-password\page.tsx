'use client';

import React, { useState } from "react";
import Input from "@/shared/Input";
import Link from 'next/link';
import Image from 'next/image';


const ForgotPassword = () => {
    const [email, setEmail] = useState("");
    const [message, setMessage] = useState("");
    const [error, setError] = useState("");
    const [isLoading, setIsLoading] = useState(false);

    const handlePasswordReset = async (e: React.FormEvent) => {
        e.preventDefault();
        setMessage("");
        setError("");
        setIsLoading(true);

        if (!email) {
            setError("Veuillez entrer une adresse e-mail.");
            setIsLoading(false);
            return;
        }

        try {
            const response = await fetch('/api/auth/forgot-password', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ email }),
            });

            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.error || 'Une erreur est survenue');
            }

            setMessage(data.message);
            setEmail(''); // Clear input after success
        } catch (err) {
            setError("Une erreur inattendue est survenue.");
            console.error('Reset password error:', err);
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <div className="nc-PageForgotPassword">
            <div className="container mb-24 lg:mb-32">
                <div className="max-w-md mx-auto space-y-6">
                    <h2 className="text-3xl font-semibold text-center mb-8">
                        Mot de passe oublié
                    </h2>

                    <p className="text-center text-neutral-600 dark:text-neutral-300 mb-10">
                        Entrez votre adresse e-mail pour recevoir un lien de réinitialisation.
                    </p>

                    <form onSubmit={handlePasswordReset} className="grid gap-6">
                        {message && (
                            <div className="bg-green-50 text-green-600 p-3 rounded-lg text-sm">
                                {message}
                            </div>
                        )}
                        
                        {error && (
                            <div className="bg-red-50 text-red-600 p-3 rounded-lg text-sm">
                                {error}
                            </div>
                        )}

                        <label className="block">
                            <span className="text-neutral-800 dark:text-neutral-200">
                                Adresse e-mail
                            </span>
                            <Input
                                type="email"
                                placeholder="<EMAIL>"
                                value={email}
                                onChange={(e) => setEmail(e.target.value)}
                                className="mt-1"
                                required
                                disabled={isLoading}
                            />
                        </label>

                        <button
                            type="submit"
                            className="w-full bg-booking-orange text-white py-3 rounded-lg hover:bg-booking-orange/90 transition-colors disabled:opacity-70"
                            disabled={isLoading}
                        >
                            {isLoading ? 'Envoi en cours...' : 'Envoyer le lien de réinitialisation'}
                        </button>

                        <div className="text-center">
                            <Link
                                href="/login"
                                className="text-sm text-neutral-700 hover:text-booking-orange dark:text-neutral-300"
                            >
                                Retour à la connexion
                            </Link>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    );
};

export default ForgotPassword;