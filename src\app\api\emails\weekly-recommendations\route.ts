import { NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { processRecommendationsForUser } from '@/components/emails/utils/recommendations';
import { shouldSendEmail, logSkippedEmail } from '@/utils/emailPreferences';

const BATCH_SIZE = 50;
const BATCH_DELAY = 2000;     
const USER_DELAY = 500;        

interface UserWithWishlist {
    user_id: string;
    latest_listing_id: string;
    email: string;
}

async function processBatch(users: UserWithWishlist[]) {
    const results = [];
    const supabase = await createClient();

    for (const user of users) {
        try {
            // Check if user has opted in to receive promotional emails
            const shouldSend = await shouldSendEmail(
                user.user_id,
                'similar_listings_weekly'
            );
            
            if (!shouldSend) {
                console.log(`User ${user.user_id} has opted out of promotional emails - skipping`);
                
                // Log skipped email
                await logSkippedEmail(supabase, {
                    user_id: user.user_id,
                    email: user.email,
                    email_type: 'similar_listings_weekly',
                    reference_id: user.latest_listing_id
                });
                
                results.push({
                    email: user.email,
                    success: true,
                    skipped: true,
                    reason: 'user_preferences',
                    timestamp: new Date().toISOString()
                });
                
                // Still wait between users to maintain rate limiting
                await new Promise(resolve => setTimeout(resolve, USER_DELAY));
                continue;
            }

            //  retry logic for failed attempts
            let attempts = 0;
            let success = false;
            while (attempts < 3 && !success) {
                success = await processRecommendationsForUser({
                    ...user,
                    email_type: 'similar_listings_weekly'
                });
                attempts++;
                if (!success && attempts < 3) {
                    await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1s before retry
                }
            }

            results.push({
                email: user.email,
                success,
                attempts,
                timestamp: new Date().toISOString()
            });

            console.log(`Processed user ${user.email}: ${success ? '✅' : '❌'} (${attempts} attempts)`);
            await new Promise(resolve => setTimeout(resolve, USER_DELAY));
        } catch (error) {
            console.error(`Error processing user ${user.email}:`, error);
            results.push({
                email: user.email,
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error',
                timestamp: new Date().toISOString()
            });
        }
    }

    return results;
}

export async function POST(request: Request) {
    try {
        // Add request validation
        if (!request.headers.has('x-api-key')) {
            return new Response('Missing API key', { status: 400 });
        }

        // 1. Verify API key for cron job
        const apiKey = request.headers.get('x-api-key');
        if (!process.env.CRON_SECRET || apiKey !== process.env.CRON_SECRET) {
            console.error('Unauthorized access attempt to weekly recommendations');
            return new Response('Unauthorized', { status: 401 });
        }

        // Add timestamp logging for performance monitoring
        const startTime = Date.now();
        console.log(`🔄 Starting weekly recommendations job at ${new Date().toISOString()}`);

        const supabase = await createClient();

        // Add timeout for the RPC call
        const timeoutPromise = new Promise((_, reject) => 
            setTimeout(() => reject(new Error('RPC timeout')), 30000)
        );
        const rpcPromise = supabase.rpc('get_users_latest_wishlists');
        
        const { data, error: userError } = await Promise.race([rpcPromise, timeoutPromise]) as any;
        const users = data as UserWithWishlist[];

        if (userError || !users) {
            console.error('Error fetching users:', userError);
            return NextResponse.json({
                error: 'Failed to fetch users',
                details: userError?.message
            }, { status: 500 });
        }

        if (users.length === 0) {
            console.log('No users with wishlists found');
            return NextResponse.json({
                message: 'No users to process',
                summary: {
                    total: 0,
                    successful: 0,
                    failed: 0,
                    timestamp: new Date().toISOString()
                }
            });
        }

        console.log(`Found ${users.length} users with wishlists to process`);

        // 4. Process users in batches with rate limiting
        const results = [];
        const totalBatches = Math.ceil(users.length / BATCH_SIZE);

        for (let i = 0; i < users.length; i += BATCH_SIZE) {
            const batchNumber = Math.floor(i / BATCH_SIZE) + 1;
            const batch = users.slice(i, i + BATCH_SIZE);

            console.log(`Processing batch ${batchNumber}/${totalBatches} (${batch.length} users)`);

            // Process batch
            const batchResults = await processBatch(batch);
            results.push(...batchResults);

            // Wait before next batch (if not last batch)
            if (i + BATCH_SIZE < users.length) {
                await new Promise(resolve => setTimeout(resolve, BATCH_DELAY));
            }
        }

        // 5. Prepare summary
        const successful = results.filter(r => r.success).length;
        const failed = results.filter(r => !r.success).length;

        // Add execution time to summary
        const summary = {
            total: users.length,
            successful,
            failed,
            completion_rate: `${((successful / users.length) * 100).toFixed(1)}%`,
            timestamp: new Date().toISOString(),
            execution_time_ms: Date.now() - startTime
        };

        console.log('✅ Weekly recommendations completed:', summary);

        return NextResponse.json({
            message: 'Weekly recommendations processed',
            summary,
            details: results
        });

    } catch (error) {
        // Add more detailed error logging
        console.error('🔴 Error in weekly recommendations:', {
            error: error instanceof Error ? error.message : 'Unknown error',
            timestamp: new Date().toISOString(),
            stack: error instanceof Error ? error.stack : undefined
        });

        return NextResponse.json(
            {
                error: 'Failed to process weekly recommendations',
                details: error instanceof Error ? error.message : 'Unknown error'
            },
            { status: 500 }
        );
    }
}