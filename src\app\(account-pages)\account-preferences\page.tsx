"use client"

import { useState, useEffect } from "react"
import { useToast } from "@/hooks/use-toast"
import { useUser } from "@/contexts/UserContext"
import { Switch } from "@/components/ui/custom-switch"
import { Mail, Bell, Tag, AlertCircle } from "lucide-react"

const AccountPreferencesPage = () => {
  const { toast } = useToast()
  const { userProfile } = useUser()
  const [isLoading, setIsLoading] = useState(false)
  const [isInitialLoading, setIsInitialLoading] = useState(true)

  // Email preference states with defaults
  const [preferences, setPreferences] = useState({
    receiveNewsletter: true,
    receiveBookingUpdates: true,
    receivePromotions: false,
    receiveSystemNotifications: true,
  })

  // Store original preferences to detect changes
  const [originalPreferences, setOriginalPreferences] = useState({
    receiveNewsletter: true,
    receiveBookingUpdates: true,
    receivePromotions: false,
    receiveSystemNotifications: true,
  })

  // Load user preferences from API
  useEffect(() => {
    const loadUserPreferences = async () => {
      try {
        setIsInitialLoading(true)

        // Only proceed with loading preferences if userProfile exists
        if (userProfile?.id) {
          const response = await fetch("/api/user-preferences")
          const data = await response.json()

          if (!response.ok) {
            throw new Error(data.error || "Failed to load preferences")
          }

          console.log("Loaded preferences:", data.preferences)

          if (data.preferences) {
            const loadedPrefs = {
              receiveNewsletter: data.preferences.receiveNewsletter ?? true,
              receiveBookingUpdates: data.preferences.receiveBookingUpdates ?? true,
              receivePromotions: data.preferences.receivePromotions ?? false,
              receiveSystemNotifications: data.preferences.receiveSystemNotifications ?? true,
            }
            setPreferences(loadedPrefs)
            setOriginalPreferences(loadedPrefs)
          }
        }
      } catch (error) {
        console.error("Error in loadUserPreferences:", error)
        toast({
          title: "Erreur",
          description: "Impossible de charger vos préférences. Veuillez réessayer.",
          variant: "destructive",
        })
      } finally {
        // Always set loading to false when done, even if userProfile doesn't exist
        setIsInitialLoading(false)
      }
    }

    // Call the function whenever userProfile changes (including from null to a value)
    loadUserPreferences()
  }, [userProfile, toast])

  // Handle toggle change
  const handleToggleChange = async (preference: keyof typeof preferences) => {
    try {
      setIsLoading(true)

      // Update local state immediately for responsive UI
      const updatedPreferences = {
        ...preferences,
        [preference]: !preferences[preference],
      }
      setPreferences(updatedPreferences)

      // Only make API call if user is logged in
      if (userProfile?.id) {
        const response = await fetch("/api/user-preferences", {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(updatedPreferences),
        })

        const data = await response.json()

        if (!response.ok) {
          // Revert the change if API call fails
          setPreferences(preferences)
          throw new Error(data.error || "Failed to update preferences")
        }

        // Update original preferences to match current preferences
        setOriginalPreferences(updatedPreferences)

        toast({
          title: "Préférences enregistrées",
          description: "Vos préférences de notifications ont été mises à jour avec succès.",
          variant: "default",
        })
      } else {
        toast({
          title: "Erreur",
          description: "Vous devez être connecté pour enregistrer vos préférences.",
          variant: "destructive",
        })
        // Revert the change if user is not logged in
        setPreferences(preferences)
      }
    } catch (error) {
      console.error("Error updating preference:", error)
      toast({
        title: "Erreur",
        description: "Une erreur est survenue lors de l'enregistrement de vos préférences.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Show loading state while initially loading preferences
  if (isInitialLoading) {
    return (
        <div className="p-8">
          <div className="animate-pulse space-y-4 max-w-2xl">
            <div className="h-6 bg-gray-200 rounded w-3/4"></div>
            <div className="h-4 bg-gray-200 rounded w-full"></div>
            <div className="space-y-4 mt-6">
              {[1, 2, 3, 4].map((i) => (
                  <div key={i} className="flex justify-between items-center p-4 border-b">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-gray-200 rounded-full"></div>
                      <div className="space-y-2">
                        <div className="h-4 bg-gray-200 rounded w-24"></div>
                        <div className="h-3 bg-gray-200 rounded w-40"></div>
                      </div>
                    </div>
                    <div className="w-10 h-5 bg-gray-200 rounded-full"></div>
                  </div>
              ))}
            </div>
          </div>
        </div>
    )
  }

  return (
      <div className="p-8">
        <div className="max-w-2xl">
          <h2 className="text-2xl font-medium text-black mb-2">Préférence de notifications</h2>
          <p className="text-gray-500 mb-6">
            Gérez vos préférences de notifications par email pour personnaliser votre expérience.
          </p>

          <div className="space-y-4">
            {/* Newsletter */}
            <div className="flex justify-between items-center p-4 border-b">
              <div className="flex items-start space-x-3">
                <Mail className="w-5 h-5 mt-0.5 text-gray-600" />
                <div>
                  <h3 className="font-medium">Newsletter</h3>
                  <p className="text-sm text-gray-500 mt-1">
                    Recevez nos actualités, conseils de voyage et offres spéciales directement dans votre boîte de
                    réception.
                  </p>
                </div>
              </div>
              <Switch
                  checked={preferences.receiveNewsletter}
                  onCheckedChange={() => handleToggleChange("receiveNewsletter")}
                  disabled={isLoading}
              />
            </div>

            {/* Booking Updates */}
            <div className="flex justify-between items-center p-4 border-b">
              <div className="flex items-start space-x-3">
                <Bell className="w-5 h-5 mt-0.5 text-gray-600" />
                <div>
                  <h3 className="font-medium">Mises à jour de réservation</h3>
                  <p className="text-sm text-gray-500 mt-1">
                    Soyez informé des changements concernant vos réservations, confirmations et rappels.
                  </p>
                </div>
              </div>
              <Switch
                  checked={preferences.receiveBookingUpdates}
                  onCheckedChange={() => handleToggleChange("receiveBookingUpdates")}
                  disabled={isLoading}
              />
            </div>

            {/* Promotional Offers */}
            <div className="flex justify-between items-center p-4 border-b">
              <div className="flex items-start space-x-3">
                <Tag className="w-5 h-5 mt-0.5 text-gray-600" />
                <div>
                  <h3 className="font-medium">Offres promotionnelles</h3>
                  <p className="text-sm text-gray-500 mt-1">
                    Recevez des offres spéciales, des réductions et des codes promotionnels exclusifs.
                  </p>
                </div>
              </div>
              <Switch
                  checked={preferences.receivePromotions}
                  onCheckedChange={() => handleToggleChange("receivePromotions")}
                  disabled={isLoading}
              />
            </div>

            {/* System Notifications */}
            <div className="flex justify-between items-center p-4 border-b">
              <div className="flex items-start space-x-3">
                <AlertCircle className="w-5 h-5 mt-0.5 text-gray-600" />
                <div>
                  <h3 className="font-medium">Notifications système</h3>
                  <p className="text-sm text-gray-500 mt-1">
                    Notifications importantes concernant votre compte, la sécurité et les mises à jour de service.
                  </p>
                </div>
              </div>
              <Switch
                  checked={preferences.receiveSystemNotifications}
                  onCheckedChange={() => handleToggleChange("receiveSystemNotifications")}
                  disabled={isLoading}
              />
            </div>
          </div>
        </div>
      </div>
  )
}

export default AccountPreferencesPage
