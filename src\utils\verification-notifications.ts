import { createClient } from "@/utils/supabase/server"
import { shouldSendEmail } from "@/utils/emailPreferences"

type VerificationStatus = "pending" | "approved" | "rejected"

/**
 * Sends an email notification to the user about their verification status change
 * @param userId The user's ID
 * @param status The new verification status
 * @param reason Optional reason for rejection
 */
export async function sendVerificationStatusEmail(
  userId: string,
  status: VerificationStatus,
  reason?: string
) {
  try {
    const supabase = await createClient()
    
    // Get user profile
    const { data: profile, error: profileError } = await supabase
      .from("profiles")
      .select("email, fullname, email_preferences")
      .eq("id", userId)
      .single()
    
    if (profileError || !profile) {
      console.error("Error fetching user profile:", profileError)
      return false
    }
    
    // Check if user has opted in to system notifications
    const shouldSend = await shouldSendEmail(
      userId,
      "verification_status"
    )
    
    if (!shouldSend) {
      console.log(`Skipping verification email to ${profile.email} due to user preferences`)
      return false
    }
    
    // Prepare email content based on status
    let subject = ""
    let content = ""
    
    if (status === "approved") {
      subject = "Votre identité a été vérifiée avec succès"
      content = `
        <h2>Félicitations ${profile.fullname}!</h2>
        <p>Votre identité a été vérifiée avec succès. Vous avez maintenant accès à toutes les fonctionnalités de notre plateforme.</p>
        <p>Votre badge de vérification est désormais visible sur votre profil.</p>
        <p>Merci de faire partie de notre communauté!</p>
      `
    } else if (status === "rejected") {
      subject = "Mise à jour de votre vérification d'identité"
      content = `
        <h2>Bonjour ${profile.fullname},</h2>
        <p>Nous avons examiné votre demande de vérification d'identité, mais nous n'avons pas pu la valider.</p>
        ${reason ? `<p><strong>Raison:</strong> ${reason}</p>` : ''}
        <p>Vous pouvez soumettre à nouveau votre demande avec des documents plus clairs depuis votre tableau de bord.</p>
        <p>Si vous avez des questions, n'hésitez pas à contacter notre équipe d'assistance.</p>
      `
    } else if (status === "pending") {
      subject = "Votre demande de vérification est en cours de traitement"
      content = `
        <h2>Bonjour ${profile.fullname},</h2>
        <p>Nous avons bien reçu votre demande de vérification d'identité.</p>
        <p>Notre équipe l'examine actuellement. Ce processus peut prendre jusqu'à 24-48 heures.</p>
        <p>Vous recevrez une notification dès que votre vérification sera terminée.</p>
      `
    }
    
    // Send email via your email service
    // This is a placeholder - implement with your actual email service
    console.log(`Would send email to ${profile.email} with subject: ${subject}`)
    console.log(`Email content: ${content}`)
    
    // Log the notification
    await supabase
      .from("notification_logs")
      .insert({
        user_id: userId,
        type: "verification_status",
        status: status,
        email_sent: true,
        metadata: { reason }
      })
    
    return true
  } catch (error) {
    console.error("Error sending verification status email:", error)
    return false
  }
}
