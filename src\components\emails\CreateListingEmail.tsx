import { ReactElement } from 'react';
import { EmailTemplate } from './EmailTemplate';

interface CreateListingEmailProps {
    name?: string;
    language?: 'en' | 'fr' | 'ar';
}

type TranslationContent = {
    subject: string;
    title: string;
    greeting: string;
    welcome_message: string;
    instruction: string;
    closing: string;
    team: string;
    cta_button: string;
};

type Translations = Record<'en' | 'fr' | 'ar', TranslationContent>;

export const translations: Translations = {
    en: {
        subject: 'Start hosting on Almindhar Booking',
        title: 'Welcome to Almindhar Booking platform!',
        greeting: 'Hello and welcome',
        welcome_message: 'Your registration was successful for testing our new hosting platform!',
        instruction: 'Click on the link below to add your first property - highlight its unique features, upload attractive photos, set available dates and fix a competitive price.',
        cta_button: 'Start listing your property',
        closing: 'Thanks to our new product, you can now list your properties directly on our hosting platform and reach a wide range of clients. We are excited to collaborate with you to continuously enrich our offerings and we look forward to welcoming you to our platform.',
        team: 'The Almindhar Booking Team'
    },
    fr: {
        subject: 'Commencez à héberger sur Almindhar Booking',
        title: 'Bienvenue sur la plateforme Almindharbooking !',
        greeting: 'Bonjour et bienvenue,',
        welcome_message: 'Votre inscription a été réussie pour tester notre nouvelle plateforme d\'hébergement !',
        instruction: 'Cliquez sur le lien ci-dessous pour ajouter votre premier bien immobilier - mettez en avant ses caractéristiques uniques, téléchargez des photos attractives, définissez les dates disponibles et fixez un prix compétitif.',
        cta_button: 'Commencez à lister votre propriété',
        closing: 'Grâce à notre nouveau produit, vous pouvez désormais inscrire vos propriétés directement sur notre plateforme d\'hébergement et toucher un large éventail de clients. Nous sommes ravis de collaborer avec vous pour enrichir continuellement notre offre et nous avons hâte de vous voir rejoindre notre plateforme.',
        team: 'L\'équipe Almindhar Booking'
    },
    ar: {
        subject: 'ابدأ بالاستضافة على المندهر بوكينج',
        title: 'مرحبًا بك على منصة المندهر بوكينج!',
        greeting: 'مرحبًا وأهلاً بك،',
        welcome_message: 'تم تسجيلك بنجاح لاختبار منصتنا الجديدة للإيجار!',
        instruction: 'انقر على الرابط أدناه لإضافة عقارك الأول - أبرز خصائصه الفريدة، وقم بتحميل صور جذابة، وحدد التواريخ المتاحة، وضع سعرًا تنافسيًا.',
        cta_button: 'ابدأ بإدراج عقارك',
        closing: 'بفضل منتجنا الجديد، يمكنك الآن تسجيل عقاراتك مباشرة على منصتنا والوصول إلى مجموعة واسعة من العملاء. نحن متحمسون للتعاون معك لإثراء عروضنا باستمرار ونتطلع إلى رؤيتك تنضم إلى منصتنا.',
        team: 'فريق المندهر بوكينج'
    }
};

/**
 * Generates HTML for the create listing email
 * This follows the same pattern used in other email components
 */
export function generateCreateListingEmailHtml({
    name,
    language = 'fr'
}: CreateListingEmailProps): string {
    const t = translations[language];
    const isRtl = language === 'ar';
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'https://almindharbooking.com';
    const createListingUrl = `${baseUrl}/dashboard/properties/create`;

    return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>${t.subject}</title>
    </head>
    <body>
      <div style="
        padding: 0;
        max-width: 600px;
        margin: 0 auto;
        font-family: Poppins, Arial, sans-serif;
        color: #333333;
        text-align: ${isRtl ? 'right' : 'left'};
        direction: ${isRtl ? 'rtl' : 'ltr'};
      ">
        <!-- Header with Logo and House Illustration -->
        <div style="
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          padding: 20px 0 40px;
          position: relative;
        ">
          <!-- Logo on the left -->
          <img 
            src="https://api.almindharbooking.com/storage/v1/object/public/email-assets/Almindhar-booking-logo-2.png" 
            alt="Almindhar Logo" 
            style="
              height: 75px;
              margin-right: ${isRtl ? '0' : 'auto'};
              margin-left: ${isRtl ? 'auto' : '0'};
            "
          />
          
          <!-- House Illustration on the right -->
          <img 
            src="https://api.almindharbooking.com/storage/v1/object/public/email-assets/almindhar-email-illustration.png" 
            alt="House Illustration" 
            style="
              height: 100px;
              margin-left: ${isRtl ? '0' : 'auto'};
              margin-right: ${isRtl ? 'auto' : '0'};
              margin-top: 15px;
            "
          />
        </div>

        <!-- Main Content - Left aligned -->
        <div style="
          padding: 0;
          text-align: ${isRtl ? 'right' : 'left'};
        ">
          <h1 style="
            font-size: 24px;
            font-weight: bold;
            color: #0F172A;
            margin-bottom: 20px;
          ">
            ${t.title}
          </h1>
          
          <p style="margin-bottom: 15px; line-height: 1.5;">
            ${t.greeting}${name ? `, ${name}` : ''}
          </p>
          
          <p style="margin-bottom: 15px; line-height: 1.5;">
            ${t.welcome_message}
          </p>
          
          <p style="margin-bottom: 30px; line-height: 1.5;">
            ${t.instruction}
          </p>

          <!-- Closing Message - No bullet points -->
          <p style="margin-bottom: 30px; line-height: 1.5;">
            ${t.closing}
          </p>

          <!-- CTA Button -->
          <div style="margin: 30px 0;">
            <a 
              href="${createListingUrl}"
              style="
                background-color: #F05123;
                color: white;
                padding: 12px 24px;
                border-radius: 4px;
                text-decoration: none;
                font-weight: bold;
                display: inline-block;
              "
            >
              ${t.cta_button}
            </a>
          </div>
          
          <p style="margin-top: 30px; font-weight: bold;">
            ${t.team}
          </p>
        </div>
      </div>
    </body>
    </html>
    `;
}

/**
 * React component for the create listing email
 * Used for previewing the email in development
 */
export const CreateListingEmail = ({ name, language = 'fr' }: CreateListingEmailProps): ReactElement => {
    const t = translations[language];
    const isRtl = language === 'ar';
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'https://almindharbooking.com';
    const createListingUrl = `${baseUrl}/add-listing`;

    return (
        <EmailTemplate
            previewText={t.welcome_message}
            language={language}
            emailType="marketing"
            metadata={{
                templateId: 'create-listing-email',
                segment: 'host',
            }}
        >
            <div style={{
                padding: 0,
                maxWidth: '600px',
                margin: '0 auto',
                fontFamily: 'Poppins, Arial, sans-serif',
                color: '#333333',
                textAlign: isRtl ? 'right' : 'left',
                direction: isRtl ? 'rtl' : 'ltr'
            }}>
                {/* Header with Logo and House Illustration */}
                <div style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'flex-start',
                    padding: '20px 0 40px',
                    position: 'relative',
                }}>
                    {/* Logo on the left */}
                    <img
                        src="https://api.almindharbooking.com/storage/v1/object/public/email-assets/Almindhar-booking-logo-2.png"
                        alt="Almindhar Logo"
                        style={{
                            height: '75px', 
                            marginRight: isRtl ? '0' : 'auto',
                            marginLeft: isRtl ? 'auto' : '0'
                        }}
                    />

                    {/* House Illustration on the right */}
                    <img
                        src="https://api.almindharbooking.com/storage/v1/object/public/email-assets/almindhar-email-illustration.png"
                        alt="House Illustration"
                        style={{
                            height: '100px', 
                            marginLeft: isRtl ? '0' : 'auto',
                            marginRight: isRtl ? 'auto' : '0',
                            marginTop: '20px' 
                        }}
                    />
                </div>

                {/* Main Content - Left aligned */}
                <div style={{
                    padding: '0',
                    textAlign: isRtl ? 'right' : 'left', 
                }}>
                    <h1 style={{
                        fontSize: '24px',
                        fontWeight: 'bold',
                        color: '#0F172A',
                        marginBottom: '20px',
                    }}>
                        {t.title}
                    </h1>

                    <p style={{ marginBottom: '15px', lineHeight: '1.5' }}>
                        {t.greeting}{name ? `, ${name}` : ''}
                    </p>

                    <p style={{ marginBottom: '15px', lineHeight: '1.5' }}>
                        {t.welcome_message}
                    </p>

                    <p style={{ marginBottom: '30px', lineHeight: '1.5' }}>
                        {t.instruction}
                    </p>

                    {/* Closing Message - No bullet points */}
                    <p style={{ marginBottom: '30px', lineHeight: '1.5' }}>
                        {t.closing}
                    </p>

                    {/* CTA Button */}
                    <div style={{ margin: '30px 0' }}>
                        <a
                            href={createListingUrl}
                            style={{
                                backgroundColor: '#F05123', 
                                color: 'white',
                                padding: '12px 24px',
                                borderRadius: '4px',
                                textDecoration: 'none',
                                fontWeight: 'bold',
                                display: 'inline-block',
                            }}
                        >
                            {t.cta_button}
                        </a>
                    </div>

                    <p style={{ marginTop: '30px', fontWeight: 'bold' }}>
                        {t.team}
                    </p>
                </div>
            </div>
        </EmailTemplate>
    );
};