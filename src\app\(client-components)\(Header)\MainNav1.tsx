import React, { <PERSON> } from "react";
import Logo from "@/shared/Logo";
import Navigation from "@/shared/Navigation/Navigation";
import SearchDropdown from "./SearchDropdown";
import ButtonPrimary from "@/shared/ButtonPrimary";
import MenuBar from "@/shared/MenuBar";
import SwitchDarkMode from "@/shared/SwitchDarkMode";
import HeroSearchForm2MobileFactory from "../(HeroSearchForm2Mobile)/HeroSearchForm2MobileFactory";
import LangDropdown from "./LangDropdown";
import { UserNav } from "@/components/user-nav";

export interface MainNav1Props {
  className?: string;
  user?: any;
}

const MainNav1: FC<MainNav1Props> = ({ className = "", user }) => {
  return (
    <div className={`nc-MainNav1 relative z-10 bg-white dark:bg-neutral-900 ${className}`}>
      <div className="container">
        <div className="h-20 flex justify-between">
          <div className="hidden md:flex justify-start flex-1 space-x-4 sm:space-x-10">
            <Logo className="w-40 self-center" />
            <Navigation />
          </div>

          <div className="flex lg:hidden flex-[3] max-w-lg !mx-auto md:px-3 ">
            <div className="self-center flex-1">
              <HeroSearchForm2MobileFactory />
            </div>
          </div>

          <div className="hidden md:flex flex-shrink-0 justify-end flex-1 lg:flex-none text-neutral-700 dark:text-neutral-100">
            <div className="hidden xl:flex space-x-0.5">
              <SwitchDarkMode />
              <SearchDropdown className="flex items-center" />
              <div className="px-1" />
              {!user ? (
                <ButtonPrimary className="self-center" href="/login">
                  S&apos;inscrire
                </ButtonPrimary>
              ) : (
                <UserNav user={user} />
              )}
            </div>

            <div className="flex xl:hidden items-center">
              <SwitchDarkMode />
              <div className="px-0.5" />
              <MenuBar />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MainNav1;
