"use client"

import { Di<PERSON>Footer } from "@/components/ui/dialog"

import { useState, useEffect, useCallback } from "react"
import { createClient } from "@/utils/supabase/client"
import { Check, X, Clock, Eye, Search, RefreshCw, ChevronLeft, ChevronRight, FileText, ImageIcon, Camera, ZoomIn, ZoomOut, RotateCw } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { toast } from "@/hooks/use-toast"
import Image from "next/image"
import { cn } from "@/lib/utils"

interface VerificationRequest {
  id: string
  user_id: string
  id_type: string
  id_number: string
  front_image_path: string
  back_image_path: string
  additional_image_path?: string
  status: string // Changed to string to accept any status value
  created_at: string
  user_email?: string
  user_name?: string
  originalStatus?: string
  rejection_reason?: string
}

export default function IdentityVerificationsTab() {
  const [verifications, setVerifications] = useState<VerificationRequest[]>([])
  const [filteredVerifications, setFilteredVerifications] = useState<VerificationRequest[]>([])
  const [loading, setLoading] = useState(true)
  const [refreshing, setRefreshing] = useState(false)
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState<string>("all")
  const [selectedVerification, setSelectedVerification] = useState<VerificationRequest | null>(null)
  const [isDetailsOpen, setIsDetailsOpen] = useState(false)
  const [isImagePreviewOpen, setIsImagePreviewOpen] = useState(false)
  const [previewImage, setPreviewImage] = useState<string | null>(null)
  const [previewImageAlt, setPreviewImageAlt] = useState("")
  const [previewImageIndex, setPreviewImageIndex] = useState(0)
  const [frontImageUrl, setFrontImageUrl] = useState<string | null>(null)
  const [backImageUrl, setBackImageUrl] = useState<string | null>(null)
  const [additionalImageUrl, setAdditionalImageUrl] = useState<string | null>(null)
  const [rejectionReason, setRejectionReason] = useState("")
  const [isRejectDialogOpen, setIsRejectDialogOpen] = useState(false)
  const [isUpdating, setIsUpdating] = useState(false)

  const supabase = createClient()

  // Map database status to UI status
  const mapDbStatusToUiStatus = (dbStatus: string): string => {
    if (!dbStatus) return "pending"

    const status = dbStatus.toLowerCase()
    if (status === "approuved" || status === "approved") return "verified"
    if (status === "declined" || status === "rejected") return "rejected"
    return "pending"
  }

  // Map UI status to filter status
  const mapUiStatusToFilterStatus = (uiStatus: string): string[] => {
    if (uiStatus === "verified") return ["approuved", "approved"]
    if (uiStatus === "rejected") return ["rejected", "declined"]
    return [uiStatus]
  }

  const fetchVerifications = useCallback(async () => {
    try {
      setLoading(true)
      setRefreshing(true)
      // First, get all verifications
      const { data: verificationData, error: verificationError } = await supabase
        .from("identity_verifications")
        .select("*")
        .order("created_at", { ascending: false })

      if (verificationError) {
        console.error("Error fetching verifications:", verificationError)
        return
      }

      console.log("Fetched verification data:", verificationData)

      // If we have verifications, try to get user information
      if (verificationData && verificationData.length > 0) {
        try {
          // Try to get user information from profiles table if it exists
          const { data: profilesData, error: profilesError } = await supabase
            .from("profiles")
            .select("id, email, fullname")
            .in(
              "id",
              verificationData.map((v) => v.user_id),
            )

          // Create a map of user data for quick lookup
          interface UserData {
            id: string
            email?: string
            fullname?: string
          }

          let userMap: Record<string, UserData> = {}

          if (!profilesError && profilesData && profilesData.length > 0) {
            // If profiles table exists and has data, use it
            userMap = profilesData.reduce<Record<string, UserData>>((map, user) => {
              map[user.id] = user
              return map
            }, {})
          } else {
            // If no profiles table or error, just use the verification data as is
            console.log("No profiles data available or error:", profilesError)
          }

          // Combine verification data with user data and map status
          const mappedData = verificationData.map((item) => ({
            ...item,
            user_email: userMap[item.user_id]?.email || "Email non disponible",
            user_name: userMap[item.user_id]?.fullname || "Utilisateur " + item.user_id.substring(0, 8),
            // Keep the original status from DB but map it for UI display
            originalStatus: item.status,
            status: mapDbStatusToUiStatus(item.status),
          }))

          console.log("Mapped verification data:", mappedData)
          setVerifications(mappedData)
          setFilteredVerifications(mappedData)
        } catch (error) {
          console.error("Error processing user data:", error)
          // Continue with just the verification data
          const mappedData = verificationData.map((item) => ({
            ...item,
            user_email: "Email non disponible",
            user_name: "Utilisateur " + item.user_id.substring(0, 8),
            // Keep the original status from DB but map it for UI display
            originalStatus: item.status,
            status: mapDbStatusToUiStatus(item.status),
          }))
          setVerifications(mappedData)
          setFilteredVerifications(mappedData)
        }
      } else {
        setVerifications([])
        setFilteredVerifications([])
      }
    } catch (error) {
      console.error("Error fetching verifications:", error)
    } finally {
      setLoading(false)
      setRefreshing(false)
    }
  }, [supabase])

  const filterVerifications = useCallback(() => {
    let filtered = [...verifications]

    // Filter by search term
    if (searchTerm) {
      const term = searchTerm.toLowerCase()
      filtered = filtered.filter(
        (v) =>
          v.user_email?.toLowerCase().includes(term) ||
          v.user_name?.toLowerCase().includes(term) ||
          v.id_number.toLowerCase().includes(term),
      )
    }

    // Filter by status - map UI status to DB status for filtering
    if (statusFilter !== "all") {
      const filterStatuses = mapUiStatusToFilterStatus(statusFilter)
      filtered = filtered.filter(
        (v) =>
          filterStatuses.includes(v.originalStatus?.toLowerCase() || "") ||
          v.status?.toLowerCase() === statusFilter.toLowerCase(),
      )
    }

    setFilteredVerifications(filtered)
  }, [searchTerm, statusFilter, verifications])

  useEffect(() => {
    fetchVerifications()
  }, [fetchVerifications])

  useEffect(() => {
    filterVerifications()
  }, [searchTerm, statusFilter, verifications, filterVerifications])

  const openImagePreview = (imageUrl: string, alt: string, index: number) => {
    setPreviewImage(imageUrl)
    setPreviewImageAlt(alt)
    setPreviewImageIndex(index)
    setIsImagePreviewOpen(true)
  }

  const navigateImages = (direction: "next" | "prev") => {
    // Create an array of available images
    const images = [
      { url: frontImageUrl, alt: "Recto du document" },
      { url: backImageUrl, alt: "Verso du document" },
      { url: additionalImageUrl, alt: "Image supplémentaire" },
    ].filter((img) => img.url !== null)

    if (images.length <= 1) return

    let newIndex = previewImageIndex
    if (direction === "next") {
      newIndex = (previewImageIndex + 1) % images.length
    } else {
      newIndex = (previewImageIndex - 1 + images.length) % images.length
    }

    setPreviewImageIndex(newIndex)
    setPreviewImage(images[newIndex].url as string)
    setPreviewImageAlt(images[newIndex].alt)
  }

  const viewVerificationDetails = async (verification: VerificationRequest) => {
    setSelectedVerification(verification)
    setFrontImageUrl(null)
    setBackImageUrl(null)
    setAdditionalImageUrl(null)

    try {
      // Get URLs for the images
      if (verification.front_image_path) {
        try {
          console.log("Front image path:", verification.front_image_path)
          
          // Try to get a public URL first
          const frontPublicUrl = supabase.storage
            .from("identity-documents")
            .getPublicUrl(verification.front_image_path)
          
          console.log("Front public URL:", frontPublicUrl)
          
          if (frontPublicUrl.data.publicUrl) {
            setFrontImageUrl(frontPublicUrl.data.publicUrl)
          } else {
            // Fallback to signed URL if public URL doesn't work
            const { data: frontData, error: frontError } = await supabase.storage
              .from("identity-documents")
              .createSignedUrl(verification.front_image_path, 60)

            if (frontError) {
              console.error("Error getting front image URL:", frontError)
            } else {
              setFrontImageUrl(frontData.signedUrl)
            }
          }
        } catch (error) {
          console.error("Error with front image:", error)
        }
      }

      if (verification.back_image_path) {
        try {
          console.log("Back image path:", verification.back_image_path)
          
          // Try to get a public URL first
          const backPublicUrl = supabase.storage
            .from("identity-documents")
            .getPublicUrl(verification.back_image_path)
          
          console.log("Back public URL:", backPublicUrl)
          
          if (backPublicUrl.data.publicUrl) {
            setBackImageUrl(backPublicUrl.data.publicUrl)
          } else {
            // Fallback to signed URL if public URL doesn't work
            const { data: backData, error: backError } = await supabase.storage
              .from("identity-documents")
              .createSignedUrl(verification.back_image_path, 60)

            if (backError) {
              console.error("Error getting back image URL:", backError)
            } else {
              setBackImageUrl(backData.signedUrl)
            }
          }
        } catch (error) {
          console.error("Error with back image:", error)
        }
      }

      if (verification.additional_image_path) {
        try {
          console.log("Additional image path:", verification.additional_image_path)
          
          // Try to get a public URL first
          const additionalPublicUrl = supabase.storage
            .from("identity-documents")
            .getPublicUrl(verification.additional_image_path)
          
          console.log("Additional public URL:", additionalPublicUrl)
          
          if (additionalPublicUrl.data.publicUrl) {
            setAdditionalImageUrl(additionalPublicUrl.data.publicUrl)
          } else {
            // Fallback to signed URL if public URL doesn't work
            const { data: additionalData, error: additionalError } = await supabase.storage
              .from("identity-documents")
              .createSignedUrl(verification.additional_image_path, 60)

            if (additionalError) {
              console.error("Error getting additional image URL:", additionalError)
            } else {
              setAdditionalImageUrl(additionalData.signedUrl)
            }
          }
        } catch (error) {
          console.error("Error with additional image:", error)
        }
      }

      setIsDetailsOpen(true)
    } catch (error) {
      console.error("Error preparing verification details:", error)
      toast({
        title: "Erreur",
        description: "Impossible de charger les détails de vérification",
        variant: "destructive",
      })
    }
  }

  const updateVerificationStatus = async (status: "pending" | "verified" | "rejected") => {
    if (!selectedVerification) return

    if (status === "rejected" && !isRejectDialogOpen) {
      setIsRejectDialogOpen(true)
      return
    }

    try {
      setIsUpdating(true)
      console.log(`Updating verification ${selectedVerification.id} to status: ${status}`)

      // Use the regular update endpoint for all statuses
      const response = await fetch("/api/update-verification", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          id: selectedVerification.id,
          status,
          ...(status === "rejected" ? { rejectionReason: rejectionReason.trim() || "Rejected by admin" } : {}),
        }),
      })

      const responseData = await response.json()
      console.log("API response:", responseData)

      if (!response.ok) {
        throw new Error(responseData.error || "Failed to update verification status")
      }

      // Close all dialogs
      setIsDetailsOpen(false)
      setIsRejectDialogOpen(false)
      
      // Reset rejection reason
      setRejectionReason("")

      // Show success message
      toast({
        title: "Statut mis à jour",
        description: `La vérification a été marquée comme ${
          status === "verified" ? "vérifiée" : status === "rejected" ? "rejetée" : "en attente"
        }`,
      })

      // Fetch fresh data from the server
      await fetchVerifications()

      setSelectedVerification(null)
    } catch (error) {
      console.error("Error updating verification status:", error)
      toast({
        title: "Erreur",
        description:
          error instanceof Error ? error.message : "Une erreur est survenue lors de la mise à jour du statut",
        variant: "destructive",
      })
    } finally {
      setIsUpdating(false)
    }
  }

  // Function to get status badge with appropriate styling
  const getStatusBadge = (status: string) => {
    let uiStatus = ""
    let variant: "default" | "destructive" | "outline" | "secondary" | "success" = "default"
    let icon = null

    switch (status) {
      case "pending":
        uiStatus = "En attente"
        variant = "outline"
        icon = <Clock size={14} className="mr-1" />
        break
      case "verified":
        uiStatus = "Vérifié"
        variant = "success"
        icon = <Check size={14} className="mr-1" />
        break
      case "rejected":
        uiStatus = "Rejeté"
        variant = "destructive"
        icon = <X size={14} className="mr-1" />
        break
      default:
        uiStatus = status || "Inconnu"
        break
    }

    return (
      <Badge variant={variant} className="flex items-center">
        {icon}
        {uiStatus}
      </Badge>
    )
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return new Intl.DateTimeFormat("fr-FR", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    }).format(date)
  }

  if (loading && !refreshing) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="flex flex-col items-center gap-2">
          <RefreshCw className="h-8 w-8 animate-spin text-primary" />
          <p className="text-muted-foreground">Chargement des vérifications...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
        <h2 className="text-2xl font-bold tracking-tight">Vérifications d&apos;identité</h2>
        <Button
          variant="outline"
          size="sm"
          onClick={fetchVerifications}
          disabled={refreshing}
          className="flex items-center gap-2"
        >
          <RefreshCw size={16} className="text-muted-foreground" />
          Actualiser
        </Button>
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4 mb-6">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Rechercher par email, nom ou numéro de document..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-9"
          />
        </div>
        <Select
          value={statusFilter}
          onValueChange={(value) => setStatusFilter(value)}
        >
          <SelectTrigger className="w-full sm:w-[180px]">
            <SelectValue placeholder="Tous les statuts" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">Tous les statuts</SelectItem>
            <SelectItem value="pending">En attente</SelectItem>
            <SelectItem value="verified">Vérifié</SelectItem>
            <SelectItem value="rejected">Rejeté</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Verification List */}
      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="flex flex-col items-center gap-2">
            <RefreshCw className="h-8 w-8 animate-spin text-primary" />
            <p className="text-muted-foreground">Chargement des vérifications...</p>
          </div>
        </div>
      ) : filteredVerifications.length === 0 ? (
        <div className="flex flex-col items-center justify-center h-64 border rounded-lg bg-muted/20">
          <div className="flex flex-col items-center gap-2 max-w-md text-center p-6">
            {searchTerm || statusFilter !== "all" ? (
              <>
                <Search className="h-8 w-8 text-muted-foreground mb-2" />
                <h3 className="text-lg font-medium">Aucun résultat trouvé</h3>
                <p className="text-muted-foreground">
                  Aucune vérification ne correspond à vos critères de recherche. Essayez de modifier vos filtres.
                </p>
                <Button 
                  variant="outline" 
                  className="mt-2"
                  onClick={() => {
                    setSearchTerm("");
                    setStatusFilter("all");
                  }}
                >
                  Réinitialiser les filtres
                </Button>
              </>
            ) : (
              <>
                <div className="p-3 rounded-full bg-muted mb-2">
                  <FileText className="h-6 w-6 text-muted-foreground" />
                </div>
                <h3 className="text-lg font-medium">Aucune vérification</h3>
                <p className="text-muted-foreground">
                  Il n&apos;y a actuellement aucune demande de vérification d&apos;identité.
                </p>
              </>
            )}
          </div>
        </div>
      ) : (
        <div className="rounded-md border">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="bg-muted/50">
                  <th className="px-4 py-3 text-left text-sm font-medium text-muted-foreground">Utilisateur</th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-muted-foreground">Type de document</th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-muted-foreground">Numéro</th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-muted-foreground">Date de soumission</th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-muted-foreground">Statut</th>
                  <th className="px-4 py-3 text-right text-sm font-medium text-muted-foreground">Actions</th>
                </tr>
              </thead>
              <tbody className="divide-y">
                {filteredVerifications.map((verification) => (
                  <tr 
                    key={verification.id} 
                    className="hover:bg-muted/30 transition-colors"
                  >
                    <td className="px-4 py-3 text-sm">
                      <div className="flex flex-col">
                        <span className="font-medium">{verification.user_name || "Utilisateur inconnu"}</span>
                        <span className="text-muted-foreground text-xs">{verification.user_email}</span>
                      </div>
                    </td>
                    <td className="px-4 py-3 text-sm">
                      {verification.id_type === "cin" ? (
                        <span className="inline-flex items-center gap-1">
                          <FileText size={14} className="text-primary" />
                          Carte d&apos;identité
                        </span>
                      ) : verification.id_type === "passport" ? (
                        <span className="inline-flex items-center gap-1">
                          <FileText size={14} className="text-primary" />
                          Passeport
                        </span>
                      ) : verification.id_type === "permit" ? (
                        <span className="inline-flex items-center gap-1">
                          <FileText size={14} className="text-primary" />
                          Permis de conduire
                        </span>
                      ) : (
                        <span className="inline-flex items-center gap-1">
                          <FileText size={14} className="text-muted-foreground" />
                          {verification.id_type || "Non spécifié"}
                        </span>
                      )}
                    </td>
                    <td className="px-4 py-3 text-sm font-mono">
                      {verification.id_number || "N/A"}
                    </td>
                    <td className="px-4 py-3 text-sm">
                      {formatDate(verification.created_at)}
                    </td>
                    <td className="px-4 py-3 text-sm">
                      {getStatusBadge(verification.status)}
                    </td>
                    <td className="px-4 py-3 text-sm text-right">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => viewVerificationDetails(verification)}
                        className="hover:bg-primary/10"
                      >
                        <Eye size={16} className="mr-1" /> Voir détails
                      </Button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}
      {/* Verification Details Dialog */}
      <Dialog open={isDetailsOpen} onOpenChange={setIsDetailsOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="text-xl flex items-center gap-2">
              Détails de vérification d&apos;identité
              {selectedVerification && (
                <span className="ml-2">{getStatusBadge(selectedVerification.status)}</span>
              )}
            </DialogTitle>
            <DialogDescription>
              Informations détaillées sur la demande de vérification d&apos;identité
            </DialogDescription>
          </DialogHeader>

          {selectedVerification && (
            <div className="space-y-6">
              {/* User Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Informations utilisateur</h3>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <span className="font-semibold text-sm min-w-[80px]">Nom:</span> 
                      <span className="text-sm">{selectedVerification.user_name || "Utilisateur inconnu"}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="font-semibold text-sm min-w-[80px]">Email:</span> 
                      <span className="text-sm">{selectedVerification.user_email}</span>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Informations document</h3>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <span className="font-semibold text-sm min-w-[80px]">Type:</span>
                      <span className="text-sm">
                        {selectedVerification.id_type === "cin"
                          ? "Carte d'identité"
                          : selectedVerification.id_type === "passport"
                            ? "Passeport"
                            : selectedVerification.id_type === "permit"
                              ? "Permis de conduire"
                              : selectedVerification.id_type || "Non spécifié"}
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="font-semibold text-sm min-w-[80px]">Numéro:</span>
                      <span className="text-sm font-mono">{selectedVerification.id_number}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="font-semibold text-sm min-w-[80px]">Soumis le:</span>
                      <span className="text-sm">{formatDate(selectedVerification.created_at)}</span>
                    </div>
                    {selectedVerification.status === "rejected" && selectedVerification.rejection_reason && (
                      <div className="flex items-start gap-2">
                        <span className="font-semibold text-sm min-w-[80px]">Raison du rejet:</span>
                        <span className="text-sm text-destructive">{selectedVerification.rejection_reason}</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Documents Section */}
              <div className="space-y-4">
                <h3 className="text-base font-medium">Documents soumis</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {/* Front ID Image */}
                  <div className="space-y-2">
                    <p className="text-sm font-medium flex items-center gap-1">
                      <FileText size={16} className="text-primary" />
                      Recto du document
                    </p>
                    <div className="relative">
                      {frontImageUrl ? (
                        <div
                          className="relative aspect-[3/2] w-full overflow-hidden rounded-md border border-muted cursor-pointer group"
                          onClick={() => openImagePreview(frontImageUrl, "Recto du document", 0)}
                        >
                          <div style={{ width: '100%', height: '100%' }}>
                            <Image
                              src={frontImageUrl}
                              alt="Recto du document"
                              fill
                              className="object-contain"
                              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 80vw, 70vw"
                              priority
                              unoptimized
                              onError={(e) => {
                                console.error("Error loading front image");
                                e.currentTarget.src = "/placeholder.svg";
                              }}
                            />
                          </div>
                          <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors flex items-center justify-center opacity-0 group-hover:opacity-100">
                            <Eye size={24} className="text-white" />
                          </div>
                        </div>
                      ) : (
                        <div className="flex flex-col items-center justify-center aspect-[3/2] w-full rounded-md border border-dashed border-muted-foreground/30 bg-muted/20 p-4">
                          <ImageIcon size={24} className="text-muted-foreground mb-2" />
                          <p className="text-sm text-muted-foreground text-center">Image non disponible</p>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Back ID Image */}
                  <div className="space-y-2">
                    <p className="text-sm font-medium flex items-center gap-1">
                      <FileText size={16} className="text-primary" />
                      Verso du document
                    </p>
                    <div className="relative">
                      {backImageUrl ? (
                        <div
                          className="relative aspect-[3/2] w-full overflow-hidden rounded-md border border-muted cursor-pointer group"
                          onClick={() => openImagePreview(backImageUrl, "Verso du document", 1)}
                        >
                          <div style={{ width: '100%', height: '100%' }}>
                            <Image
                              src={backImageUrl}
                              alt="Verso du document"
                              fill
                              className="object-contain"
                              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 80vw, 70vw"
                              priority
                              unoptimized
                              onError={(e) => {
                                console.error("Error loading back image");
                                e.currentTarget.src = "/placeholder.svg";
                              }}
                            />
                          </div>
                          <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors flex items-center justify-center opacity-0 group-hover:opacity-100">
                            <Eye size={24} className="text-white" />
                          </div>
                        </div>
                      ) : (
                        <div className="flex flex-col items-center justify-center aspect-[3/2] w-full rounded-md border border-dashed border-muted-foreground/30 bg-muted/20 p-4">
                          <ImageIcon size={24} className="text-muted-foreground mb-2" />
                          <p className="text-sm text-muted-foreground text-center">Image non disponible</p>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Additional Image (Selfie) */}
                  <div className="space-y-2">
                    <p className="text-sm font-medium flex items-center gap-1">
                      <Camera size={16} className="text-primary" />
                      Image supplémentaire
                    </p>
                    <div className="relative">
                      {additionalImageUrl ? (
                        <div
                          className="relative aspect-[3/2] w-full overflow-hidden rounded-md border border-muted cursor-pointer group"
                          onClick={() => openImagePreview(additionalImageUrl, "Image supplémentaire", 2)}
                        >
                          <div style={{ width: '100%', height: '100%' }}>
                            <Image
                              src={additionalImageUrl}
                              alt="Image supplémentaire"
                              fill
                              className="object-contain"
                              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 80vw, 70vw"
                              priority
                              unoptimized
                              onError={(e) => {
                                console.error("Error loading additional image");
                                e.currentTarget.src = "/placeholder.svg";
                              }}
                            />
                          </div>
                          <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors flex items-center justify-center opacity-0 group-hover:opacity-100">
                            <Eye size={24} className="text-white" />
                          </div>
                        </div>
                      ) : (
                        <div className="flex flex-col items-center justify-center aspect-[3/2] w-full rounded-md border border-dashed border-muted-foreground/30 bg-muted/20 p-4">
                          <ImageIcon size={24} className="text-muted-foreground mb-2" />
                          <p className="text-sm text-muted-foreground text-center">Image non disponible</p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <DialogFooter>
                {selectedVerification.status === "pending" && (
                  <div className="flex flex-col sm:flex-row gap-2 w-full sm:justify-end">
                    <Button
                      variant="outline"
                      className="border-destructive text-destructive hover:bg-destructive/10"
                      onClick={() => updateVerificationStatus("rejected")}
                    >
                      <X className="h-4 w-4 mr-2" />
                      Rejeter
                    </Button>
                    <Button
                      variant="default"
                      className="bg-green-600 hover:bg-green-700"
                      onClick={() => updateVerificationStatus("verified")}
                    >
                      <Check className="h-4 w-4 mr-2" />
                      Approuver
                    </Button>
                  </div>
                )}
              </DialogFooter>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Rejection Reason Dialog */}
      <Dialog open={isRejectDialogOpen} onOpenChange={setIsRejectDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Raison du rejet</DialogTitle>
            <DialogDescription>
              Veuillez indiquer la raison pour laquelle cette vérification est rejetée. Cette information sera visible par l&apos;utilisateur.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <label htmlFor="rejection-reason" className="text-sm font-medium">
                Raison du rejet
              </label>
              <textarea
                id="rejection-reason"
                className="w-full min-h-[100px] rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                placeholder="Ex: Document illisible, informations incomplètes..."
                value={rejectionReason}
                onChange={(e) => setRejectionReason(e.target.value)}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => {
              setIsRejectDialogOpen(false);
              setRejectionReason("");
            }}>
              Annuler
            </Button>
            <Button 
              variant="destructive" 
              onClick={async () => {
                if (rejectionReason.trim()) {
                  await updateVerificationStatus("rejected");
                }
              }}
              disabled={!rejectionReason.trim() || isUpdating}
            >
              {isUpdating ? "En cours..." : "Confirmer le rejet"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Image Preview Dialog */}
      <Dialog open={isImagePreviewOpen} onOpenChange={setIsImagePreviewOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden p-0 gap-0">
          <div className="relative bg-black h-[80vh] flex items-center justify-center">
            {/* Image */}
            <div className="relative w-full h-full flex items-center justify-center overflow-hidden">
              {previewImage ? (
                <div className="relative w-full h-full flex items-center justify-center">
                  {/* 
                    Using standard HTML img tag instead of Next.js Image component
                    because we need to display dynamically generated URLs from Supabase storage
                    and the Image component has limitations with dynamic URLs in modal contexts.
                    This approach provides better reliability for viewing verification documents.
                  */}
                  <img
                    src={previewImage}
                    alt={previewImageAlt}
                    className="max-w-full max-h-full object-contain"
                    onError={(e) => {
                      console.error("Error loading preview image");
                      e.currentTarget.src = "/placeholder.svg";
                    }}
                  />
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center p-8 text-white">
                  <ImageIcon size={48} className="mb-4 text-gray-400" />
                  <p>Image non disponible</p>
                </div>
              )}
            </div>

            {/* Navigation Controls */}
            <div className="absolute inset-x-0 bottom-0 flex justify-between p-4 bg-gradient-to-t from-black/80 to-transparent">
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="icon"
                  className="rounded-full bg-black/50 border-white/20 text-white hover:bg-black/70"
                  onClick={() => navigateImages("prev")}
                >
                  <ChevronLeft size={20} />
                </Button>
                <div className="text-white text-sm">
                  {previewImageAlt}
                </div>
              </div>
              <Button
                variant="outline"
                size="icon"
                className="rounded-full bg-black/50 border-white/20 text-white hover:bg-black/70"
                onClick={() => navigateImages("next")}
              >
                <ChevronRight size={20} />
              </Button>
            </div>

            {/* Close Button */}
            <Button
              variant="outline"
              size="icon"
              className="absolute top-4 right-4 rounded-full bg-black/50 border-white/20 text-white hover:bg-black/70"
              onClick={() => setIsImagePreviewOpen(false)}
            >
              <X size={18} />
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
