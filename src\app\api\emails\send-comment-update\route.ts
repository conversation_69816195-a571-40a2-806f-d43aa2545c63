import { NextResponse } from 'next/server';
import { processCommentEmail } from '@/components/emails/utils/comment';

interface WebhookPayload {
  type: 'INSERT' | 'UPDATE';
  table: string;
  record: {
    id: string;
    listing_id: string;
    user_id: string;
    rating: number;
    comment: string;
    created_at: string;
  };
  old_record?: {
    rating: number;
    comment: string;
  };
}

export async function POST(request: Request) {
  try {
    // 1. Validate webhook payload
    const payload = await request.json() as WebhookPayload;

    if (!payload.record?.listing_id || !payload.record?.user_id || !payload.record?.id) {
      console.error('Invalid webhook payload - missing required fields:', payload);
      return NextResponse.json({ 
        error: 'Invalid webhook payload - missing required fields' 
      }, { status: 400 });
    }

    // 2. Process and send email (email preference check is done inside processCommentEmail)
    const success = await processCommentEmail(
      payload.record,
      payload.type === 'INSERT' ? 'new' : 'updated'
    );

    if (!success) {
      return NextResponse.json(
        { error: 'Failed to process comment email' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      message: 'Comment email processed successfully',
      success: true
    });
  } catch (error) {
    console.error('Error processing comment webhook:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}