"use client";

import React from "react";
import CommonLayout from "./CommonLayout";


const PageAddListingFinaliserAnnonce = () => {
    return (
        <CommonLayout params={{ stepIndex: "finaliser-annonce" }}>
            <div className="flex flex-col min-h-[70vh] max-h-screen overflow-auto h-full pb-[80px] md:pb-0 hide-scrollbar">
                {/* Custom styles for screens up to 820px */}
                <style jsx>{`
                    @media (max-width: 820px) {
                        .column-layout-820 {
                            display: block;
                        }
                        .row-layout-820 {
                            display: none;
                        }
                    }
                    @media (min-width: 821px) {
                        .column-layout-820 {
                            display: none;
                        }
                        .row-layout-820 {
                            display: flex;
                        }
                    }
                `}</style>
                {/* Mobile & Medium screens (up to 820px): Video first, then text */}
                <div className="w-full mb-6 column-layout-820">
                    <video
                        src="https://api.almindharbooking.com/storage/v1/object/public/videos//finaliser-annonce.mp4"
                        autoPlay
                        loop
                        muted
                        playsInline
                        width={800}
                        height={600}
                        className="w-full h-[400px] object-cover bg-white"
                        aria-label="Finalisation de l'annonce"
                        poster="/videos/finaliser-annonce_poster.jpg"
                    />
                </div>
                <div className="w-full px-4 py-6 md:px-0 md:py-0 max-w-2xl mx-auto column-layout-820">
                    <span className="text-gray-500 text-base mb-2 block">Étape finale</span>
                    <h1 className="font-bold text-gray-900 leading-tight text-left text-[clamp(1.5rem,5vw,2.2rem)] mb-4">
                        Paramétrez vos tarifs et disponibilités
                    </h1>
                    <p className="text-gray-500 text-base">
                        Vous avez presque terminé ! Il ne vous reste plus qu&apos;à définir le prix de votre logement, proposer d&apos;éventuelles réductions, configurer votre calendrier de disponibilités et choisir vos modalités de paiement. Ces informations sont essentielles pour attirer des voyageurs et recevoir vos premières réservations en toute sérénité.
                    </p>
                </div>
                {/* Desktop/Tablet (above 820px): two-column layout */}
                <div className="row-layout-820 md:flex-row items-center w-full gap-8 md:gap-8 xl:gap-32 mt-auto">
                    {/* Left column: Step label, heading, description */}
                    <div className="flex-1 max-w-2xl flex flex-col justify-center w-full md:pl-8 lg:pl-12">
                        <span className="text-gray-500 text-base mb-2">Étape finale</span>
                        <h1 className="font-bold text-gray-900 leading-tight text-left text-[clamp(1.5rem,5vw,2.2rem)] md:text-3xl lg:text-5xl mb-4">
                            Paramétrez vos tarifs et disponibilités
                        </h1>
                        <p className="text-gray-500 text-base md:text-lg">
                            Vous avez presque terminé ! Il ne vous reste plus qu&apos;à définir le prix de votre logement, proposer d&apos;éventuelles réductions, configurer votre calendrier de disponibilités et choisir vos modalités de paiement. Ces informations sont essentielles pour attirer des voyageurs et recevoir vos premières réservations en toute sérénité.
                        </p>
                    </div>
                    {/* Right column: Video */}
                    <div className="flex-1 flex items-end justify-end h-full">
                        <video
                            src="https://api.almindharbooking.com/storage/v1/object/public/videos//finaliser-annonce.mp4"
                            autoPlay
                            loop
                            muted
                            playsInline
                            width={800}
                            height={600}
                            className="h-auto max-h-full object-contain"
                            aria-label="Finalisation de l'annonce"
                            poster="/videos/finaliser-annonce_poster.jpg"
                        />
                    </div>
                </div>
            </div>
        </CommonLayout>
    );
};

export default PageAddListingFinaliserAnnonce; 