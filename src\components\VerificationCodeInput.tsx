import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, Loader, Phone, ArrowRight } from 'lucide-react';

interface VerificationCodeInputProps {
    isOpen: boolean;
    phoneNumber: string;
    onClose: () => void;
    onVerify: (code: string) => void;
    onResend: () => void;
}

const VerificationCodeInput: React.FC<VerificationCodeInputProps> = ({
    isOpen,
    phoneNumber,
    onClose,
    onVerify,
    onResend,
}) => {
    const [code, setCode] = useState(['', '', '', '', '', '']);
    const [isLoading, setIsLoading] = useState(false);
    const [timeLeft, setTimeLeft] = useState(30); // 30 seconds countdown
    const inputRefs = useRef<(HTMLInputElement | null)[]>([]);

    useEffect(() => {
        if (isOpen) {
            inputRefs.current[0]?.focus();
        }
    }, [isOpen]);

    useEffect(() => {
        if (timeLeft > 0) {
            const timer = setTimeout(() => setTimeLeft(timeLeft - 1), 1000);
            return () => clearTimeout(timer);
        }
    }, [timeLeft]);

    const handleInputChange = (index: number, value: string) => {
        if (value.length > 1) value = value[0];
        if (!/^\d*$/.test(value)) return;

        const newCode = [...code];
        newCode[index] = value;
        setCode(newCode);

        // Move to next input if value is entered
        if (value && index < 5) {
            inputRefs.current[index + 1]?.focus();
        }
    };

    const handleKeyDown = (index: number, e: React.KeyboardEvent<HTMLInputElement>) => {
        if (e.key === 'Backspace' && !code[index] && index > 0) {
            inputRefs.current[index - 1]?.focus();
        }
    };

    const handlePaste = (e: React.ClipboardEvent) => {
        e.preventDefault();
        const pastedData = e.clipboardData.getData('text').slice(0, 6);
        if (!/^\d+$/.test(pastedData)) return;

        const newCode = [...code];
        pastedData.split('').forEach((char, index) => {
            if (index < 6) newCode[index] = char;
        });
        setCode(newCode);
    };

    const handleSubmit = async () => {
        const fullCode = code.join('');
        if (fullCode.length !== 6) return;

        setIsLoading(true);
        await onVerify(fullCode);
        setIsLoading(false);
    };

    const handleResend = () => {
        onResend();
        setTimeLeft(30);
    };

    return (
        <AnimatePresence>
            {isOpen && (
                <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50"
                >
                    <motion.div
                        initial={{ scale: 0.95 }}
                        animate={{ scale: 1 }}
                        exit={{ scale: 0.95 }}
                        className="bg-white rounded-xl shadow-xl p-6 w-full max-w-md mx-4"
                    >
                        <div className="flex justify-between items-center mb-6">
                            <h3 className="text-xl font-semibold text-gray-900">
                                Vérifier Votre Téléphone
                            </h3>
                            <button
                                onClick={onClose}
                                className="p-2 hover:bg-gray-100 rounded-full transition-colors"
                            >
                                <X className="w-5 h-5 text-gray-500" />
                            </button>
                        </div>

                        <div className="text-center mb-6">
                            <div className="flex items-center justify-center gap-2 text-gray-600 mb-2">
                                <Phone className="w-5 h-5" />
                                <span>{phoneNumber}</span>
                            </div>
                            <p className="text-sm text-gray-500">
                                Nous avons envoyé un code de vérification à 6 chiffres sur votre téléphone
                            </p>
                        </div>

                        <div className="flex justify-center gap-2 mb-6">
                            {code.map((digit, index) => (
                                <input
                                    key={index}
                                    ref={el => inputRefs.current[index] = el}
                                    type="text"
                                    maxLength={1}
                                    value={digit}
                                    onChange={(e) => handleInputChange(index, e.target.value)}
                                    onKeyDown={(e) => handleKeyDown(index, e)}
                                    onPaste={handlePaste}
                                    className="w-12 h-12 text-center text-xl font-semibold border-2 
                                        rounded-lg focus:border-[#EA580D] focus:ring-[#EA580D]"
                                />
                            ))}
                        </div>

                        <button
                            onClick={handleSubmit}
                            disabled={code.join('').length !== 6 || isLoading}
                            className="w-full py-3 bg-[#EA580D] text-white rounded-lg
                                hover:bg-[#EA580D]/90 transition-colors disabled:opacity-50
                                disabled:cursor-not-allowed flex items-center justify-center gap-2"
                        >
                            {isLoading ? (
                                <Loader className="w-5 h-5 animate-spin" />
                            ) : (
                                <>
                                    <span>Vérifier Code</span>
                                    <ArrowRight className="w-5 h-5" />
                                </>
                            )}
                        </button>

                        <div className="mt-4 text-center">
                            <button
                                onClick={handleResend}
                                disabled={timeLeft > 0}
                                className="text-[#EA580D] hover:text-[#EA580D]/80 text-sm font-medium
                                    disabled:text-gray-400 disabled:cursor-not-allowed"
                            >
                                {timeLeft > 0 ? (
                                    `Resend code in ${timeLeft}s`
                                ) : (
                                    'Resend code'
                                )}
                            </button>
                        </div>
                    </motion.div>
                </motion.div>
            )}
        </AnimatePresence>
    );
};

export default VerificationCodeInput;