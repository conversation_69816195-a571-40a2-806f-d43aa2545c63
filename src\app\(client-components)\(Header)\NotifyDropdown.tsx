"use client"

import { FC, Fragment, useState, useEffect, useCallback, useRef } from "react"
import { Popover, Transition } from "@headlessui/react"
import { BellIcon, ArrowPathIcon, CheckIcon, EnvelopeOpenIcon } from "@heroicons/react/24/outline"
import { useNotifications } from "@/hooks/useNotifications"
import { NotificationIcon } from "@/components/notifications/NotificationIcon"
import { format } from "date-fns"
import { fr } from 'date-fns/locale'
import { useRouter } from "next/navigation"

interface Props {
  className?: string
}

const NotifyDropdown: FC<Props> = ({ className = "" }) => {
  const { notifications, unreadCount, markAsRead, markAllAsRead, loading, refreshing, forceRefresh } = useNotifications()
  const router = useRouter()
  const timeoutRef = useRef<NodeJS.Timeout | null>(null)
  const buttonRef = useRef<HTMLButtonElement>(null)
  
  // Add state to track panel visibility and prevent flickering
  const [isPanelOpen, setIsPanelOpen] = useState(false)
  const [cachedNotifications, setCachedNotifications] = useState(notifications)
  
  // Update cached notifications when they change and we're not actively loading a refresh
  useEffect(() => {
    if (!refreshing && notifications.length > 0) {
      setCachedNotifications(notifications)
    }
  }, [notifications, refreshing])
  
  // Clean up timeout when component unmounts
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
        timeoutRef.current = null
      }
    }
  }, [])
  
  // Handle refreshing notifications safely
  const handleRefresh = useCallback(() => {
    // Clear any existing timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }
    
    // Refresh notifications
    forceRefresh()
    
    // Set a safety timeout in case loading gets stuck
    timeoutRef.current = setTimeout(() => {
      if (loading || refreshing) {
        console.log("Loading timed out - forcing refresh")
        forceRefresh()
      }
    }, 5000)
  }, [forceRefresh, loading, refreshing])

  // Handle clicking on a notification
  const handleNotificationClick = useCallback(async (notification: any) => {
    await markAsRead(notification.id)

    // Handle different notification types
    switch (notification.type) {
      case 'MESSAGE_RECEIVED':
        if (notification.data?.conversation_id) {
          router.push(`/messages?conversation=${notification.data.conversation_id}`)
        }
        break
      case 'BOOKING_REQUEST':
        if (notification.data?.booking_id) {
          router.push(`dashboard/bookings`)
        }
        break
      case 'BOOKING_ACCEPTED':
      case 'BOOKING_DECLINED':
        if (notification.data?.booking_id) {
          router.push(`/bookings`)
        } 
        break
      case 'LISTING_APPROVED':
      case 'LISTING_REJECTED':
        if (notification.data?.listing_id) {
          router.push(`/dashboard/properties/listings`)
        }
        break
      default:
        console.warn('Unknown notification type:', notification.type)
    }
  }, [markAsRead, router])

  // Handle marking a notification as read without navigating
  const handleMarkAsRead = useCallback(async (e: React.MouseEvent, notificationId: string) => {
    e.stopPropagation()
    await markAsRead(notificationId)
  }, [markAsRead])
  
  // Handle marking all notifications as read
  const handleMarkAllAsRead = useCallback(async (e: React.MouseEvent) => {
    e.stopPropagation()
    await markAllAsRead()
  }, [markAllAsRead])
  
  // Handle panel transitions
  const handleOpenPanel = useCallback(() => {
    setIsPanelOpen(true)
    // Only refresh data when we open the panel if we have no cached data
    if (cachedNotifications.length === 0 && !loading && !refreshing) {
      handleRefresh()
    }
  }, [handleRefresh, cachedNotifications.length, loading, refreshing])
  
  const handleClosePanel = useCallback(() => {
    setIsPanelOpen(false)
  }, [])

  // Determine which notifications to display - use cached data to prevent flickering
  const displayNotifications = cachedNotifications.length > 0 ? cachedNotifications : notifications
  const isFirstLoad = loading && displayNotifications.length === 0
  
  return (
    <div className={`relative flex items-center ${className}`}>
      <Popover className="flex items-center">
        {({ open, close }) => (
          <>
            <Popover.Button
              ref={buttonRef}
              onClick={() => {
                if (!open) {
                  handleOpenPanel()
                }
              }}
              className={`
                ${open ? "bg-orange-50 dark:bg-orange-900/20" : ""} 
                group relative self-center w-9 h-9 sm:w-10 sm:h-10 
                hover:bg-gray-100 dark:hover:bg-gray-800 
                rounded-full inline-flex items-center justify-center
                transition-all duration-200 ease-in-out
                focus:outline-none focus-visible:ring-2 focus-visible:ring-orange-500
              `}
            >
              {unreadCount > 0 && (
                <span className="absolute top-1.5 right-1.5 sm:top-2 sm:right-2 w-1.5 h-1.5 sm:w-2 sm:h-2 bg-booking-orange rounded-full 
                                  animate-pulse transition-all duration-300" />
              )}
              <BellIcon className="h-4 w-4 sm:h-5 sm:w-5 text-gray-700 dark:text-gray-200
                                   group-hover:text-booking-orange dark:group-hover:text-booking-orange
                                   transition-colors duration-200" />
            </Popover.Button>

            <Transition
              as={Fragment}
              enter="transition ease-out duration-200"
              enterFrom="opacity-0 translate-y-1"
              enterTo="opacity-100 translate-y-0"
              leave="transition ease-in duration-150"
              leaveFrom="opacity-100 translate-y-0"
              leaveTo="opacity-0 translate-y-1"
              afterEnter={handleOpenPanel}
              beforeLeave={handleClosePanel}
              show={open}
            >
              <Popover.Panel 
                static
                className="absolute z-50 w-screen max-w-xs sm:max-w-sm px-4 
                          mt-0 top-full -right-2 sm:right-0 sm:px-0"
              >
                <div className="overflow-hidden rounded-2xl shadow-lg ring-1 ring-orange-500/10 
                                dark:ring-orange-400/10 bg-white dark:bg-gray-800 mt-1">
                  <div className="relative">
                    {/* Header */}
                    <div className="px-4 sm:px-6 py-2.5 sm:py-3 border-b border-gray-200 dark:border-gray-700
                                           flex justify-between items-center">
                      <h3 className="text-base sm:text-lg font-semibold text-gray-900 dark:text-gray-100">
                        Notifications
                      </h3>
                      <div className="flex items-center gap-1 sm:gap-2">
                        {unreadCount > 0 && (
                          <span className="text-xs sm:text-sm text-gray-500 dark:text-gray-400">
                            {unreadCount} non lu{unreadCount > 1 ? 's' : ''}
                          </span>
                        )}
                        <button 
                          onClick={(e) => {
                            e.stopPropagation()
                            handleRefresh()
                          }} 
                          className="p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors relative group"
                          aria-label="Refresh notifications"
                          type="button"
                          disabled={refreshing}
                        >
                          <ArrowPathIcon className={`h-3.5 w-3.5 sm:h-4 sm:w-4 text-gray-500 ${refreshing ? 'animate-spin text-orange-500' : ''}`} />
                          <span className="hidden sm:block absolute -bottom-10 right-0 w-24 bg-gray-900 text-white text-xs rounded py-1 px-2 opacity-0 group-hover:opacity-100 pointer-events-none transition-opacity">
                            Actualiser
                          </span>
                        </button>
                        {unreadCount > 0 && (
                          <button 
                            onClick={handleMarkAllAsRead} 
                            className="p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors relative group"
                            aria-label="Mark all as read"
                            type="button"
                          >
                            <EnvelopeOpenIcon className="h-3.5 w-3.5 sm:h-4 sm:w-4 text-gray-500" />
                            <span className="hidden sm:block absolute -bottom-10 right-0 w-32 bg-gray-900 text-white text-xs rounded py-1 px-2 opacity-0 group-hover:opacity-100 pointer-events-none transition-opacity z-50">
                              Tout marquer comme lu
                            </span>
                          </button>
                        )}
                      </div>
                    </div>

                    {/* Notifications List */}
                    <div className="max-h-[50vh] sm:max-h-[70vh] overflow-y-auto overscroll-contain
                                    scrollbar-thin scrollbar-thumb-rounded-full
                                    scrollbar-track-transparent hover:scrollbar-track-transparent
                                    scrollbar-thumb-gray-300 hover:scrollbar-thumb-gray-400
                                    dark:scrollbar-thumb-gray-600 dark:hover:scrollbar-thumb-gray-500
                                    transition-colors duration-200">
                      {isFirstLoad ? (
                        <div className="flex flex-col items-center justify-center py-6 sm:py-8">
                          <div className="animate-spin rounded-full h-6 w-6 sm:h-8 sm:w-8 
                                         border-b-2 border-booking-orange mb-2" />
                          <p className="text-xs sm:text-sm text-gray-500">Chargement des notifications...</p>
                        </div>
                      ) : displayNotifications.length === 0 ? (
                        <div className="flex flex-col items-center justify-center py-6 sm:py-8 px-4">
                          <BellIcon className="h-8 w-8 sm:h-12 sm:w-12 text-gray-400 mb-2" />
                          <p className="text-xs sm:text-sm text-gray-500 dark:text-gray-400 text-center">
                            Aucune notification
                          </p>
                        </div>
                      ) : (
                        <div className="divide-y divide-gray-200 dark:divide-gray-700">
                          {displayNotifications.map((item) => (
                            <div 
                              key={item.id}
                              className={`relative group w-full p-3 sm:p-4 hover:bg-orange-50/50 dark:hover:bg-orange-900/10
                                          transition-colors duration-150 ease-in-out
                                          ${!item.is_read ? "bg-orange-50/50 dark:bg-orange-900/10" : ""}
                                          flex items-start gap-2 sm:gap-3`}
                            >
                              <button
                                onClick={() => {
                                  close()
                                  handleNotificationClick(item)
                                }}
                                className="flex-1 text-left min-w-0 flex items-start gap-2 sm:gap-3"
                                type="button"
                              >
                                <div className="flex-shrink-0 mt-1">
                                  <NotificationIcon 
                                    type={item.type} 
                                    className={`h-4 w-4 sm:h-5 sm:w-5 ${
                                      item.type === 'MESSAGE_RECEIVED' ? 'text-blue-500' :
                                      item.type === 'BOOKING_REQUEST' ? 'text-orange-500' :
                                      item.type === 'BOOKING_ACCEPTED' || item.type === 'LISTING_APPROVED' ? 'text-green-500' :
                                      item.type === 'BOOKING_DECLINED' || item.type === 'LISTING_REJECTED' ? 'text-red-500' :
                                      'text-gray-500'
                                    }`} 
                                  />
                                </div>
                                <div className="flex-1 text-left min-w-0">
                                  <p className="text-xs sm:text-sm font-medium text-gray-900 dark:text-gray-100">
                                    {item.title}
                                  </p>
                                  <p className="text-xs sm:text-sm text-gray-500 dark:text-gray-400 line-clamp-2">
                                    {item.message}
                                  </p>
                                  <p className="text-[10px] sm:text-xs text-gray-400 dark:text-gray-500 mt-1">
                                    {format(new Date(item.created_at), 'dd MMM, HH:mm', { locale: fr })}
                                  </p>
                                </div>
                              </button>
                              
                              {!item.is_read && (
                                <>
                                  <span className="absolute right-3 top-3 h-1.5 w-1.5 sm:right-4 sm:top-4 sm:h-2 sm:w-2
                                                  bg-orange-500 rounded-full"></span>
                                  <button
                                    onClick={(e) => handleMarkAsRead(e, item.id)}
                                    className="flex-shrink-0 opacity-0 group-hover:opacity-100
                                            transition-opacity duration-200
                                            p-0.5 bg-white/80 dark:bg-gray-800/80 hover:bg-white dark:hover:bg-gray-700 
                                            rounded-full border border-gray-200 dark:border-gray-700"
                                    aria-label="Mark as read"
                                    type="button"
                                  >
                                    <CheckIcon className="h-3 w-3 sm:h-3.5 sm:w-3.5 text-green-500" />
                                  </button>
                                </>
                              )}
                            </div>
                          ))}
                          
                          {/* Add a subtle loading indicator at the bottom when refreshing with existing data */}
                          {refreshing && displayNotifications.length > 0 && (
                            <div className="p-2 flex justify-center">
                              <div className="w-5 h-5 border-t-2 border-booking-orange rounded-full animate-spin"></div>
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </Popover.Panel>
            </Transition>
          </>
        )}
      </Popover>
    </div>
  )
}

export default NotifyDropdown