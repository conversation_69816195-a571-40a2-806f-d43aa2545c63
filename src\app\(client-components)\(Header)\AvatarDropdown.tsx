"use client"

import { memo } from "react"
import Avatar from "@/shared/Avatar"
import Link from "next/link"
import type { Profile } from "@/types/interfaces"
import { useUser } from "@/contexts/UserContext"
import { toast } from "react-toastify"
import { useRouter } from "next/navigation"

/**
 * Props for the AvatarDropdown component
 */
export interface AvatarDropdownProps {
  userProfile?: Profile
  onLogout?: () => void
  isLoading?: boolean
}

const AvatarDropdown = memo(({ userProfile, isLoading: externalLoading }: AvatarDropdownProps) => {
  const router = useRouter()
  const { logout } = useUser()

  // Use external loading state
  const isLoading = externalLoading

  const handleLogout = async () => {
    try {
      await logout()
      router.refresh()
      router.push("/")
    } catch (error) {
      console.error("Error logging out:", error)
      toast.error("Failed to log out")
    }
  }

  if (isLoading) {
    return (
        <div className="self-center w-10 h-10 sm:w-12 sm:h-12 flex items-center justify-center">
          <div className="w-8 h-8 sm:w-9 sm:h-9 animate-pulse bg-neutral-200 dark:bg-neutral-700 rounded-full" />
        </div>
    )
  }

  // If not logged in show log in button
  if (!userProfile) {
    return (
        <div className="self-center">
          <Link
              href="/login"
              className="inline-flex items-center justify-center px-4 py-2 text-sm font-medium text-white bg-booking-orange hover:bg-booking-orange rounded-full transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          >
            Se connecter
          </Link>
        </div>
    )
  }

  // If logged in, show avatar that links to account page
  return (
      <Link
          href="/account"
          className="self-center w-10 h-10 sm:w-12 sm:h-12 rounded-full text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-800 focus:outline-none transition-all duration-300 flex items-center justify-center"
          aria-label="Account"
      >
        <div className="relative">
          <Avatar
              sizeClass="w-8 h-8 sm:w-9 sm:h-9"
              imgUrl={userProfile?.avatar_url || undefined}
              userName={userProfile?.fullname || userProfile?.email || ""}
              priority={true}
          />
        </div>
      </Link>
  )
})

AvatarDropdown.displayName = "AvatarDropdown"

export default AvatarDropdown
